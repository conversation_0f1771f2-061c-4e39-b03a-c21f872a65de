# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o requirements.lock
absl-py==2.3.1
    # via ortools
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   httpx
    #   jupyter-server
appnope==0.1.4
    # via ipykernel
argon2-cffi==25.1.0
    # via jupyter-server
argon2-cffi-bindings==21.2.0
    # via argon2-cffi
arrow==1.3.0
    # via isoduration
asttokens==3.0.0
    # via stack-data
async-lru==2.0.5
    # via jupyterlab
attrs==25.3.0
    # via
    #   jsonschema
    #   referencing
babel==2.17.0
    # via jupyterlab-server
beautifulsoup4==4.13.4
    # via nbconvert
bleach==6.2.0
    # via nbconvert
certifi==2025.7.9
    # via
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via
    #   argon2-cffi-bindings
    #   clarabel
charset-normalizer==3.4.2
    # via requests
clarabel==0.11.1
    # via cvxpy
cloudpickle==3.1.1
    # via shap
comm==0.2.2
    # via
    #   ipykernel
    #   ipywidgets
contourpy==1.3.0
    # via matplotlib
cvxpy==1.6.6
    # via ropwr
cycler==0.12.1
    # via matplotlib
debugpy==1.8.14
    # via ipykernel
decorator==5.2.1
    # via ipython
defusedxml==0.7.1
    # via nbconvert
et-xmlfile==2.0.0
    # via openpyxl
exceptiongroup==1.3.0
    # via
    #   anyio
    #   ipython
executing==2.2.0
    # via stack-data
fastjsonschema==2.21.1
    # via nbformat
fonttools==4.58.5
    # via matplotlib
fqdn==1.5.1
    # via jsonschema
h11==0.16.0
    # via httpcore
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via jupyterlab
idna==3.10
    # via
    #   anyio
    #   httpx
    #   jsonschema
    #   requests
immutabledict==4.2.1
    # via ortools
importlib-metadata==8.7.0
    # via
    #   jupyter-client
    #   jupyter-lsp
    #   jupyterlab
    #   jupyterlab-server
    #   nbconvert
importlib-resources==6.5.2
    # via matplotlib
ipykernel==6.29.5
    # via
    #   innovation-model-investigation (pyproject.toml)
    #   jupyter
    #   jupyter-console
    #   jupyterlab
ipython==8.18.1
    # via
    #   ipykernel
    #   ipywidgets
    #   jupyter-console
ipywidgets==8.1.7
    # via jupyter
isoduration==20.11.0
    # via jsonschema
jedi==0.19.2
    # via ipython
jinja2==3.1.6
    # via
    #   jupyter-server
    #   jupyterlab
    #   jupyterlab-server
    #   nbconvert
    #   osqp
joblib==1.5.1
    # via
    #   osqp
    #   scikit-learn
json5==0.12.0
    # via jupyterlab-server
jsonpointer==3.0.0
    # via jsonschema
jsonschema==4.24.0
    # via
    #   jupyter-events
    #   jupyterlab-server
    #   nbformat
jsonschema-specifications==2025.4.1
    # via jsonschema
jupyter==1.1.1
    # via innovation-model-investigation (pyproject.toml)
jupyter-client==8.6.3
    # via
    #   ipykernel
    #   jupyter-console
    #   jupyter-server
    #   nbclient
jupyter-console==6.6.3
    # via jupyter
jupyter-core==5.8.1
    # via
    #   ipykernel
    #   jupyter-client
    #   jupyter-console
    #   jupyter-server
    #   jupyterlab
    #   nbclient
    #   nbconvert
    #   nbformat
jupyter-events==0.12.0
    # via jupyter-server
jupyter-lsp==2.2.5
    # via jupyterlab
jupyter-server==2.16.0
    # via
    #   jupyter-lsp
    #   jupyterlab
    #   jupyterlab-server
    #   notebook
    #   notebook-shim
jupyter-server-terminals==0.5.3
    # via jupyter-server
jupyterlab==4.4.4
    # via
    #   jupyter
    #   notebook
jupyterlab-pygments==0.3.0
    # via nbconvert
jupyterlab-server==2.27.3
    # via
    #   jupyterlab
    #   notebook
jupyterlab-widgets==3.0.15
    # via ipywidgets
kiwisolver==1.4.7
    # via matplotlib
llvmlite==0.43.0
    # via numba
loguru==0.7.3
    # via innovation-model-investigation (pyproject.toml)
markupsafe==3.0.2
    # via
    #   jinja2
    #   nbconvert
matplotlib==3.8.4
    # via
    #   innovation-model-investigation (pyproject.toml)
    #   optbinning
    #   scorecardpy
    #   seaborn
matplotlib-inline==0.1.7
    # via
    #   ipykernel
    #   ipython
mistune==3.1.3
    # via nbconvert
nbclient==0.10.2
    # via nbconvert
nbconvert==7.16.6
    # via
    #   jupyter
    #   jupyter-server
nbformat==5.10.4
    # via
    #   jupyter-server
    #   nbclient
    #   nbconvert
nest-asyncio==1.6.0
    # via ipykernel
notebook==7.4.4
    # via jupyter
notebook-shim==0.2.4
    # via
    #   jupyterlab
    #   notebook
numba==0.60.0
    # via shap
numpy==1.26.4
    # via
    #   innovation-model-investigation (pyproject.toml)
    #   clarabel
    #   contourpy
    #   cvxpy
    #   matplotlib
    #   numba
    #   optbinning
    #   ortools
    #   osqp
    #   pandas
    #   patsy
    #   ropwr
    #   scikit-learn
    #   scipy
    #   scorecardpy
    #   scs
    #   seaborn
    #   shap
    #   statsmodels
openpyxl==3.1.5
    # via innovation-model-investigation (pyproject.toml)
optbinning==0.20.1
    # via innovation-model-investigation (pyproject.toml)
ortools==9.11.4210
    # via optbinning
osqp==1.0.4
    # via cvxpy
overrides==7.7.0
    # via jupyter-server
packaging==25.0
    # via
    #   ipykernel
    #   jupyter-events
    #   jupyter-server
    #   jupyterlab
    #   jupyterlab-server
    #   matplotlib
    #   nbconvert
    #   plotly
    #   shap
    #   statsmodels
pandas==2.3.1
    # via
    #   innovation-model-investigation (pyproject.toml)
    #   optbinning
    #   ortools
    #   scorecardpy
    #   seaborn
    #   shap
    #   statsmodels
pandocfilters==1.5.1
    # via nbconvert
parso==0.8.4
    # via jedi
patsy==1.0.1
    # via
    #   scorecardpy
    #   statsmodels
pexpect==4.9.0
    # via ipython
pillow==11.3.0
    # via matplotlib
platformdirs==4.3.8
    # via jupyter-core
plotly==5.24.1
    # via innovation-model-investigation (pyproject.toml)
prometheus-client==0.22.1
    # via jupyter-server
prompt-toolkit==3.0.51
    # via
    #   ipython
    #   jupyter-console
protobuf==5.26.1
    # via ortools
psutil==7.0.0
    # via ipykernel
ptyprocess==0.7.0
    # via
    #   pexpect
    #   terminado
pure-eval==0.2.3
    # via stack-data
pycparser==2.22
    # via cffi
pydantic==2.11.7
    # via innovation-model-investigation (pyproject.toml)
pydantic-core==2.33.2
    # via pydantic
pygments==2.19.2
    # via
    #   ipython
    #   jupyter-console
    #   nbconvert
pyparsing==3.2.3
    # via matplotlib
python-dateutil==2.9.0.post0
    # via
    #   arrow
    #   jupyter-client
    #   matplotlib
    #   pandas
python-json-logger==3.3.0
    # via jupyter-events
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via jupyter-events
pyzmq==27.0.0
    # via
    #   ipykernel
    #   jupyter-client
    #   jupyter-console
    #   jupyter-server
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
    #   jupyter-events
requests==2.32.4
    # via jupyterlab-server
rfc3339-validator==0.1.4
    # via
    #   jsonschema
    #   jupyter-events
rfc3986-validator==0.1.1
    # via
    #   jsonschema
    #   jupyter-events
ropwr==1.1.0
    # via optbinning
rpds-py==0.26.0
    # via
    #   jsonschema
    #   referencing
scikit-learn==1.5.2
    # via
    #   innovation-model-investigation (pyproject.toml)
    #   optbinning
    #   ropwr
    #   scorecardpy
    #   shap
scipy==1.13.1
    # via
    #   innovation-model-investigation (pyproject.toml)
    #   clarabel
    #   cvxpy
    #   optbinning
    #   osqp
    #   ropwr
    #   scikit-learn
    #   scs
    #   shap
    #   statsmodels
scorecardpy==*******
    # via innovation-model-investigation (pyproject.toml)
scs==3.2.7.post2
    # via cvxpy
seaborn==0.13.2
    # via innovation-model-investigation (pyproject.toml)
send2trash==1.8.3
    # via jupyter-server
setuptools==80.9.0
    # via
    #   jupyterlab
    #   osqp
shap==0.42.1
    # via innovation-model-investigation (pyproject.toml)
six==1.17.0
    # via
    #   python-dateutil
    #   rfc3339-validator
slicer==0.0.7
    # via shap
sniffio==1.3.1
    # via anyio
soupsieve==2.7
    # via beautifulsoup4
stack-data==0.6.3
    # via ipython
statsmodels==0.14.5
    # via scorecardpy
tenacity==9.1.2
    # via plotly
terminado==0.18.1
    # via
    #   jupyter-server
    #   jupyter-server-terminals
threadpoolctl==3.6.0
    # via scikit-learn
tinycss2==1.4.0
    # via bleach
tomli==2.2.1
    # via jupyterlab
tornado==6.5.1
    # via
    #   ipykernel
    #   jupyter-client
    #   jupyter-server
    #   jupyterlab
    #   notebook
    #   terminado
tqdm==4.67.1
    # via shap
traitlets==5.14.3
    # via
    #   comm
    #   ipykernel
    #   ipython
    #   ipywidgets
    #   jupyter-client
    #   jupyter-console
    #   jupyter-core
    #   jupyter-events
    #   jupyter-server
    #   jupyterlab
    #   matplotlib-inline
    #   nbclient
    #   nbconvert
    #   nbformat
types-python-dateutil==2.9.0.20250708
    # via arrow
typing-extensions==4.14.1
    # via
    #   anyio
    #   async-lru
    #   beautifulsoup4
    #   exceptiongroup
    #   ipython
    #   mistune
    #   pydantic
    #   pydantic-core
    #   python-json-logger
    #   referencing
    #   typing-inspection
typing-inspection==0.4.1
    # via pydantic
tzdata==2025.2
    # via pandas
uri-template==1.3.0
    # via jsonschema
urllib3==2.5.0
    # via requests
wcwidth==0.2.13
    # via prompt-toolkit
webcolors==24.11.1
    # via jsonschema
webencodings==0.5.1
    # via
    #   bleach
    #   tinycss2
websocket-client==1.8.0
    # via jupyter-server
widgetsnbextension==4.0.14
    # via ipywidgets
xlsxwriter==3.2.5
    # via innovation-model-investigation (pyproject.toml)
zipp==3.23.0
    # via
    #   importlib-metadata
    #   importlib-resources
