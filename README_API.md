# AI训练引擎API服务

本项目提供了一个简化的API服务，用于模型训练和预测。它封装了现有的评分卡训练代码，提供了两个核心API接口：训练接口和预测接口。

## 功能特点

- **简化的API接口**：只提供训练和预测两个核心API接口
- **与Web平台集成**：支持从Web平台获取训练数据
- **模型版本管理**：支持模型的版本控制和管理
- **本地文件存储**：使用本地文件系统存储模型文件
- **健康检查和监控**：提供基本的健康检查和状态监控功能

## 安装依赖

```bash
# 使用pip安装依赖
pip install -e .
```

## 运行服务

```bash
# 直接运行app.py
python app.py

# 或者使用uvicorn
uvicorn app:app --host 0.0.0.0 --port 8000 --reload
```

服务启动后，可以通过浏览器访问 http://localhost:8000/docs 查看API文档。

## API接口

### 1. 训练API

```
POST /api/v1/train
```

**请求参数**:

```json
{
  "training_set_id": "dataset_001",
  "parameters": {
    "target_column": "is_default",
    "test_size": 0.3,
    "random_state": 42
  }
}
```

**响应**:

```json
{
  "model_id": "model_12345678",
  "version": "20250717123456",
  "metrics": {
    "accuracy": 0.85,
    "precision": 0.82,
    "recall": 0.79,
    "f1_score": 0.80,
    "auc": 0.88
  },
  "training_time": 120.5,
  "status": "success"
}
```

### 2. 预测API

```
POST /api/v1/predict
```

**请求参数**:

```json
{
  "model_id": "model_12345678",
  "data": {
    "financial_ratio_current": 1.5,
    "financial_ratio_debt": 0.4,
    "operation_innovation_capability": 0.8,
    "external_market_volatility": 0.3,
    ...
  }
}
```

**响应**:

```json
{
  "prediction": "健康",
  "confidence": 0.78,
  "explanation": {
    "feature_importance": {
      "financial_ratio_current": 0.15,
      "financial_ratio_debt": 0.22,
      "operation_innovation_capability": 0.18,
      "external_market_volatility": 0.12
    },
    "probability_healthy": 0.78,
    "probability_unhealthy": 0.22
  },
  "status": "success"
}
```

### 3. 健康检查API

```
GET /api/v1/health
```

**响应**:

```json
{
  "status": "ok",
  "version": "0.1.0",
  "uptime": 3600.5,
  "resources": {
    "cpu": 25.5,
    "memory": 40.2,
    "disk": 60.0
  },
  "active_tasks": 0
}
```

### 4. 模型列表API

```
GET /api/v1/models
```

**响应**:

```json
{
  "models": [
    {
      "model_id": "model_12345678",
      "latest_version": "20250717123456",
      "versions_count": 3,
      "created_at": "2025-07-17T12:34:56",
      "metrics": {
        "accuracy": 0.85,
        "auc": 0.88
      }
    },
    ...
  ]
}
```

### 5. 模型详情API

```
GET /api/v1/models/{model_id}
```

**响应**:

```json
{
  "model_id": "model_12345678",
  "version": "20250717123456",
  "created_at": "2025-07-17T12:34:56",
  "parameters": {
    "target_column": "is_default",
    "test_size": 0.3
  },
  "metrics": {
    "accuracy": 0.85,
    "precision": 0.82,
    "recall": 0.79,
    "f1_score": 0.80,
    "auc": 0.88
  },
  "file_path": "models/model_12345678/20250717123456/model.pkl",
  "size": 1024000,
  "training_set_id": "dataset_001"
}
```

## 目录结构

```
innovation-model-investigation/
├── api/                      # API服务代码
│   ├── models/               # 数据模型定义
│   ├── routers/              # API路由
│   ├── services/             # 服务层
│   └── utils/                # 工具函数
├── app.py                    # 主应用入口
├── models/                   # 模型存储目录
└── logs/                     # 日志目录
```

## 开发指南

### 添加新的API端点

1. 在 `api/models/` 目录下定义数据模型
2. 在 `api/services/` 目录下实现业务逻辑
3. 在 `app.py` 中添加新的路由处理函数

### 测试API

可以使用Swagger UI（http://localhost:8000/docs）或者curl命令测试API：

```bash
# 健康检查
curl -X GET "http://localhost:8000/api/v1/health"

# 训练模型
curl -X POST "http://localhost:8000/api/v1/train" \
  -H "Content-Type: application/json" \
  -d '{"training_set_id": "dataset_001", "parameters": {"target_column": "is_default"}}'
```