Metadata-Version: 2.4
Name: innovation-model-investigation
Version: 0.1.2
Summary: 企业风险评估模型权重优化项目
Author-email: Your Name <<EMAIL>>
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: pandas>=2.0.0
Requires-Dist: numpy>=1.24.0
Requires-Dist: scikit-learn>=1.3.0
Requires-Dist: scipy>=1.11.0
Requires-Dist: optbinning>=0.17.0
Requires-Dist: scorecardpy>=*******
Requires-Dist: matplotlib>=3.7.0
Requires-Dist: seaborn>=0.12.0
Requires-Dist: plotly>=5.15.0
Requires-Dist: shap>=0.42.0
Requires-Dist: jupyter>=1.0.0
Requires-Dist: pytest>=7.0.0
Requires-Dist: pydantic>=2.0.0
Requires-Dist: fastapi>=0.100.0
Requires-Dist: uvicorn>=0.22.0
Requires-Dist: python-multipart>=0.0.6
Requires-Dist: loguru>=0.7.0
Requires-Dist: psutil>=5.9.0
Provides-Extra: dev
Requires-Dist: black; extra == "dev"
Requires-Dist: isort; extra == "dev"
Requires-Dist: flake8; extra == "dev"
Requires-Dist: mypy; extra == "dev"

# 企业风险评估模型权重优化项目

## 🎯 项目背景

传统企业风险评估依赖人工"拍脑袋"设定指标权重，缺乏科学依据。本项目专注于**科创企业健康性评估**，基于已有样本数据，运用机器学习技术优化权重体系，将主观经验转化为数据驱动的科学决策。

### 科创企业评估特点
- **指标多元化**：技术创新能力、市场前景、团队实力、财务状况等多维度指标
- **动态性强**：科创企业发展阶段变化快，风险特征动态演变
- **数据稀缺**：相比传统企业，科创企业历史数据相对有限
- **评估复杂**：需要平衡创新潜力与经营风险

### 核心问题
- **现状**：30个科创健康性指标通过人工权重加权求和，得出企业健康评分
- **痛点**：权重体系主观性强，无法反映科创企业真实风险特征
- **目标**：基于科创企业历史样本数据，学习最优权重组合

## 🏗️ 技术方案架构

### 方案选择：WOE分箱 + 逻辑回归
经过深入分析，采用**WOE分箱 + 逻辑回归**作为核心技术方案，特别适合科创企业评估：

1. **保留信息完整性**：避免简单二值化造成的科创特征信息损失
2. **业务可解释性强**：WOE值具有直观的健康性含义，便于监管审查
3. **适应性强**：可处理科创企业的非线性风险特征
4. **行业通用**：评分卡方法在风险评估领域广泛应用且效果成熟

## 📊 科创企业数据处理流程

### 1. 数据准备阶段
```
科创企业数据结构：
- 企业样本：N个科创企业记录
- 健康性指标：每个企业30个指标的原始分数
  * 技术创新指标：研发投入、专利数量、技术团队等
  * 市场能力指标：市场份额、客户质量、营销能力等  
  * 财务健康指标：盈利能力、现金流、资产质量等
  * 治理结构指标：管理团队、公司治理、合规性等
- 标签数据：企业健康性标记（0=健康，1=不健康）
```

### 2. 特征工程：针对科创企业的WOE分箱

#### 2.1 科创企业分箱策略
- **技术指标分箱**：考虑研发周期，采用更宽松的分箱策略
- **财务指标分箱**：针对科创企业财务波动大的特点，采用百分位分箱
- **定性指标处理**：将团队背景、技术壁垒等定性指标量化分箱

#### 2.2 WOE编码在科创评估中的应用
```
对每个科创健康性指标的每个分箱计算：
WOE_i_j = ln((% Healthy in Bin_j) / (% Unhealthy in Bin_j))

特别关注：
- 技术创新指标的非线性关系
- 财务指标在不同发展阶段的不同权重
- 市场指标与行业周期的关联性
```

## 📚 技术文档中心

本项目提供完整的技术文档体系，涵盖科创企业健康性评估的各个方面：

### 核心文档

#### 1. [模型评估指标详解](./docs/model_metrics_guide.md)
**专为科创企业评估定制的指标体系**
- **模型性能评估指标**: AUC, KS统计量, 精确率, 召回率, F1分数, 准确率
- **模型稳定性指标**: PSI, CSI（特别重要，因为科创企业变化快）
- **评分卡专用指标**: IV值, WOE, GINI系数
- **科创业务指标**: 健康率, 风险排序能力, 分箱单调性

**适用人群**: 科创投资分析师, 风险管理人员, 模型验证人员

#### 2. [评分卡建模流程指南](./docs/scorecard_modeling_process.md)
**科创企业健康性评估的完整建模流程**
- **评分卡概述**: 在科创企业评估中的应用
- **建模流程**: 11步完整流程，针对科创企业特点优化
- **数据准备**: 科创企业数据特点、质量检查、样本定义
- **特征工程**: 科创指标分类、缺失值处理、异常值处理
- **模型训练**: 考虑科创企业样本不平衡的训练策略
- **模型验证**: 针对科创企业的验证方法

**适用人群**: 科创评估建模人员, 投资决策支持人员, 产品经理

#### 3. [WOE分箱技术详解](./docs/woe_binning_guide.md)
**深入解析适用于科创企业的WOE分箱技术**
- **WOE基础理论**: 在科创企业评估中的应用原理
- **分箱方法**: 针对科创企业指标特点的分箱策略
- **实际应用案例**: 研发投入、专利数量、团队背景等科创指标的分箱实例
- **科创特色处理**: 处理科创企业数据稀缺、波动大等问题

**适用人群**: 科创企业分析师, 评分卡建模专家, 高级数据分析师

### 使用指南

#### 新手入门路径（科创评估方向）
1. 先阅读 [评分卡建模流程指南](./docs/scorecard_modeling_process.md) 了解科创企业评估框架
2. 学习 [模型评估指标详解](./docs/model_metrics_guide.md) 掌握科创企业评估方法
3. 深入 [WOE分箱技术详解](./docs/woe_binning_guide.md) 精通科创指标处理技术

#### 专业进阶路径（科创专业化）
1. 深入研究科创企业特有的风险特征和评估难点
2. 结合监管要求建立科创企业评估标准
3. 开发适用于不同科创发展阶段的评估模型

## 🔄 实施路径

### Phase 1：科创企业数据理解
- [x] 科创企业数据特点分析
- [x] 健康性标签定义与验证
- [x] 科创指标体系梳理
- [x] 行业基准建立

### Phase 2：科创特色分箱优化
- [x] 科创指标分箱策略设计
- [x] 技术创新指标IV值分析
- [x] 财务健康指标WOE计算
- [x] 定性指标量化处理

### Phase 3：科创评估模型训练
- [x] 针对科创企业的模型训练
- [x] 考虑行业特点的权重优化
- [x] 模型稳定性验证（重要：科创企业变化快）
- [x] 业务逻辑验证

### Phase 4：科创健康性评估应用
- [x] 科创企业健康性评分系统
- [x] 投资决策支持工具
- [x] 持续监控与模型更新机制
- [x] 监管报告自动化

## 🛠️ 技术实现栈

### 核心依赖库
```python
# 评分卡专用库（适用于科创企业评估）
scorecardpy>=*******    # 专业评分卡建模
optbinning>=0.17.0      # 最优分箱算法

# 机器学习核心
scikit-learn>=1.3.0     # 逻辑回归与评估
scipy>=1.11.0           # 统计分析

# 数据处理
pandas>=2.0.0           # 数据操作
numpy>=1.24.0           # 数值计算

# 可视化分析
matplotlib>=3.7.0       # 统计图表
seaborn>=0.12.0         # 高级可视化
plotly>=5.15.0          # 交互式图表

# 模型解释
shap>=0.42.0            # 模型可解释性分析
```

## 🛠️ 主要功能模块

### 数据生成与质量评估
- **数据生成**: `scripts/data/data_sample_generate.py` - 生成科创企业评估样本数据
- **质量评估**: `scripts/data/comprehensive_data_quality_assessment.py` - 全面数据质量评估，包含IV值分析
- **特征增强**: `scripts/data/feature_engineering_enhancement.py` - 特征交互和趋势分析

### 模型训练与评估
- **标准流水线**: `scripts/run_pipeline.py` - 完整的WOE分箱+逻辑回归训练流程
- **增强流水线**: `scripts/run_enhanced_pipeline.py` - 包含特征交互的增强版本训练
- **模型监控**: `scripts/model_monitor.py` - 模型性能监控和预警

### 模型对比与分析
- **基线对比**: `scripts/model_baseline_comparison.py` - 基于baseline的统一对比分析，包含PSI漂移检测
- **传统对比**: `scripts/model_compare_traditional.py` - 模型与传统方法的详细对比分析
- **黄金样本**: `scripts/model_analyze_golden_samples.py` - 关键样本的深度分析

### 模型部署与导出
- **规则导出**: `scripts/export_scorecard_rules.py` - 将训练好的模型导出为JSON规则文件
- **评分卡生成**: 支持生成标准评分卡格式，便于业务应用

## 🚀 快速开始

### 环境准备
```bash
# 激活虚拟环境
source .venv/bin/activate

# 安装依赖
uv pip install -e .
```

### 运行科创企业评估示例
```bash
# 运行完整科创企业健康性评估流程
python scripts/run_pipeline.py

# 运行增强特征版本（包含特征交互）
python scripts/run_enhanced_pipeline.py

# 生成样本数据
python scripts/data/data_sample_generate.py

# 数据质量评估
python scripts/data/comprehensive_data_quality_assessment.py
```

### 模型对比和监控
```bash
# 基于baseline的统一对比分析（包含PSI漂移检测）
python scripts/model_baseline_comparison.py

# 传统方法对比分析
python scripts/model_compare_traditional.py

# 黄金样本分析
python scripts/model_analyze_golden_samples.py

# 模型监控
python scripts/model_monitor.py

# 导出评分卡规则
python scripts/export_scorecard_rules.py
```

### 核心模块使用
```python
# 科创企业数据加载和预处理
from src.innovation_model_investigation.data import DataLoader, DataPreprocessor

# 科创指标特征工程
from src.innovation_model_investigation.features import OptimalBinning, WOEEncoder

# 科创企业评估模型训练和评估
from src.innovation_model_investigation.models import ModelTrainer, ModelEvaluator
```

## 📈 项目成果与效果

### 技术成果
- **权重科学性**：基于科创企业历史数据学习，消除主观偏差
- **模型性能**：针对科创企业特点优化，AUC可达0.99+（测试集）
- **稳定性监控**：集成PSI漂移检测，确保模型长期稳定性
- **可解释性**：每个权重都有统计学依据，支持业务解释

### 系统功能
- **完整流水线**：从数据生成到模型部署的端到端解决方案
- **版本管理**：支持多版本模型对比和演进分析
- **质量监控**：全面的数据质量评估和模型性能监控
- **规则导出**：支持将模型导出为生产环境可用的规则文件

### 业务价值
- **投资决策**：科创企业健康性识别准确率显著提升
- **风险控制**：提前识别科创企业潜在风险，降低投资损失
- **资源配置**：基于数据驱动的决策优化资源投入策略
- **监管合规**：满足科创企业评估的监管要求，提供可审计的决策依据

## 📊 项目目录结构

```
innovation-model-investigation/
├── src/                           # 核心源代码
│   └── innovation_model_investigation/
├── scripts/                       # 工具脚本
│   ├── data/                     # 数据相关脚本
│   ├── run_pipeline.py           # 主要训练流水线
│   ├── model_baseline_comparison.py  # 模型对比分析
│   └── export_scorecard_rules.py # 规则导出
├── data/                         # 数据文件
├── outputs/                      # 模型输出结果
├── docs/                         # 技术文档
└── README.md                     # 项目说明
```

---
