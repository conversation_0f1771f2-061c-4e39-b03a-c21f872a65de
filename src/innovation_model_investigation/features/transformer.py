"""
特征转换管道
整合数据预处理、分箱、WOE编码等转换步骤
"""

import pandas as pd
import numpy as np
from typing import Dict,  Tuple,  Any
import logging
from sklearn.base import BaseEstimator, TransformerMixin

from .binning import OptimalBinning
from .woe_encoder import WOEEncoder

logger = logging.getLogger(__name__)


class FeatureTransformer(BaseEstimator, TransformerMixin):
    """特征转换管道"""
    
    def __init__(self,
                 use_optimal_binning: bool = True,
                 use_woe_encoding: bool = True,
                 min_n_bins: int = 3,
                 max_n_bins: int = 6,
                 min_bin_size: float = 0.05):
        """
        初始化特征转换器
        
        Args:
            use_optimal_binning: 是否使用最优分箱
            use_woe_encoding: 是否使用WOE编码
            min_n_bins: 最少分箱数
            max_n_bins: 最多分箱数
            min_bin_size: 每个分箱最小样本比例
        """
        self.use_optimal_binning = use_optimal_binning
        self.use_woe_encoding = use_woe_encoding
        self.min_n_bins = min_n_bins
        self.max_n_bins = max_n_bins
        self.min_bin_size = min_bin_size
        
        self.binning = None
        self.woe_encoder = None
        self.feature_columns = None
        self.fitted = False
        
    def fit(self, X: pd.DataFrame, y: pd.Series) -> 'FeatureTransformer':
        """
        拟合特征转换器
        
        Args:
            X: 特征数据
            y: 目标变量
            
        Returns:
            self
        """
        logger.info("开始拟合特征转换器...")
        
        self.feature_columns = X.columns.tolist()
        
        # 1. 最优分箱
        if self.use_optimal_binning:
            logger.info("执行最优分箱...")
            self.binning = OptimalBinning(
                min_n_bins=self.min_n_bins,
                max_n_bins=self.max_n_bins,
                min_bin_size=self.min_bin_size
            )
            X_binned = self.binning.fit_transform_all(X, y)
        else:
            X_binned = X.copy()
        
        # 2. WOE编码
        if self.use_woe_encoding:
            logger.info("执行WOE编码...")
            self.woe_encoder = WOEEncoder()
            self.woe_encoder.fit(X_binned, y)
        
        self.fitted = True
        logger.info("特征转换器拟合完成")
        
        return self
    
    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        转换特征数据
        
        Args:
            X: 待转换的特征数据
            
        Returns:
            转换后的特征数据
        """
        if not self.fitted:
            raise ValueError("请先调用fit方法拟合转换器")
        
        X_transformed = X.copy()
        
        # 确保列顺序一致
        if self.feature_columns:
            missing_cols = set(self.feature_columns) - set(X_transformed.columns)
            if missing_cols:
                logger.warning(f"缺少特征列: {missing_cols}")
            
            # 只保留训练时的特征列
            available_cols = [col for col in self.feature_columns if col in X_transformed.columns]
            X_transformed = X_transformed[available_cols]
        
        # 1. 应用最优分箱
        if self.use_optimal_binning and self.binning:
            X_transformed = self.binning.transform_all(X_transformed)
        
        # 2. 应用WOE编码
        if self.use_woe_encoding and self.woe_encoder:
            X_transformed = self.woe_encoder.transform(X_transformed)
        
        return X_transformed
    
    def fit_transform(self, X: pd.DataFrame, y: pd.Series) -> pd.DataFrame:
        """
        拟合并转换特征数据
        
        Args:
            X: 特征数据
            y: 目标变量
            
        Returns:
            转换后的特征数据
        """
        return self.fit(X, y).transform(X)
    
    def get_feature_importance(self) -> pd.DataFrame:
        """获取特征重要性信息"""
        if not self.fitted:
            raise ValueError("请先调用fit方法拟合转换器")
        
        importance_data = []
        
        if self.use_optimal_binning and self.binning:
            # 获取IV值
            iv_ranking = self.binning.get_iv_ranking()
            for _, row in iv_ranking.iterrows():
                importance_data.append({
                    'feature': row['feature'],
                    'iv_value': row['iv'],
                    'bins_count': row.get('bins_count', 0),
                    'woe_values': []
                })
        
        if self.use_woe_encoding and self.woe_encoder:
            # 获取WOE值信息
            woe_summary = self.woe_encoder.get_woe_summary()
            for feature_name, woe_info in woe_summary.items():
                # 查找对应的特征
                for item in importance_data:
                    if item['feature'] == feature_name:
                        item['woe_values'] = woe_info.get('woe_values', [])
                        break
                else:
                    # 如果没有找到，添加新的特征
                    importance_data.append({
                        'feature': feature_name,
                        'iv_value': 0.0,
                        'bins_count': len(woe_info.get('woe_values', [])),
                        'woe_values': woe_info.get('woe_values', [])
                    })
        
        return pd.DataFrame(importance_data)
    
    def get_transformation_summary(self) -> Dict[str, Any]:
        """获取转换摘要信息"""
        if not self.fitted:
            return {'error': '请先调用fit方法拟合转换器'}
        
        summary = {
            'use_optimal_binning': self.use_optimal_binning,
            'use_woe_encoding': self.use_woe_encoding,
            'feature_count': len(self.feature_columns) if self.feature_columns else 0,
            'binning_summary': {},
            'woe_summary': {}
        }
        
        if self.use_optimal_binning and self.binning:
            binning_results = self.binning.get_binning_results()
            summary['binning_summary'] = {
                'total_features_binned': len(binning_results),
                'avg_bins_per_feature': np.mean([len(result.get('bin_edges', [])) - 1 
                                               for result in binning_results.values()]),
                'iv_statistics': {
                    'mean_iv': np.mean([result.get('iv', 0) for result in binning_results.values()]),
                    'max_iv': np.max([result.get('iv', 0) for result in binning_results.values()]),
                    'min_iv': np.min([result.get('iv', 0) for result in binning_results.values()])
                }
            }
        
        if self.use_woe_encoding and self.woe_encoder:
            woe_summary = self.woe_encoder.get_woe_summary()
            summary['woe_summary'] = {
                'total_features_encoded': len(woe_summary),
                'encoding_statistics': {
                    'features_with_woe': len([f for f, info in woe_summary.items() 
                                            if len(info.get('woe_values', [])) > 0])
                }
            }
        
        return summary
    
    def plot_transformation_summary(self, figsize: Tuple[int, int] = (15, 10)) -> None:
        """绘制转换摘要图表"""
        if not self.fitted:
            logger.error("请先调用fit方法拟合转换器")
            return
        
        import matplotlib.pyplot as plt
        import seaborn as sns
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, axes = plt.subplots(2, 2, figsize=figsize)
        fig.suptitle('特征转换摘要', fontsize=16, fontweight='bold')
        
        # 1. IV值分布
        if self.use_optimal_binning and self.binning:
            iv_ranking = self.binning.get_iv_ranking()
            
            # IV值分布直方图
            axes[0, 0].hist(iv_ranking['iv'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            axes[0, 0].set_title('IV值分布')
            axes[0, 0].set_xlabel('IV值')
            axes[0, 0].set_ylabel('特征数量')
            axes[0, 0].grid(True, alpha=0.3)
            
            # Top 10特征IV值
            top_features = iv_ranking.head(10)
            y_pos = np.arange(len(top_features))
            axes[0, 1].barh(y_pos, top_features['iv'], color='lightcoral')
            axes[0, 1].set_yticks(y_pos)
            axes[0, 1].set_yticklabels([f[:20] + '...' if len(f) > 20 else f 
                                       for f in top_features['feature']])
            axes[0, 1].set_title('Top 10 特征 IV值')
            axes[0, 1].set_xlabel('IV值')
            axes[0, 1].grid(True, alpha=0.3)
        
        # 2. 分箱数量分布
        if self.use_optimal_binning and self.binning:
            binning_results = self.binning.get_binning_results()
            bins_counts = [len(result.get('bin_edges', [])) - 1 for result in binning_results.values()]
            
            bins_count_freq = pd.Series(bins_counts).value_counts().sort_index()
            axes[1, 0].bar(bins_count_freq.index, bins_count_freq.values, color='lightgreen', alpha=0.7)
            axes[1, 0].set_title('分箱数量分布')
            axes[1, 0].set_xlabel('分箱数量')
            axes[1, 0].set_ylabel('特征数量')
            axes[1, 0].grid(True, alpha=0.3)
        
        # 3. WOE编码统计
        if self.use_woe_encoding and self.woe_encoder:
            woe_summary = self.woe_encoder.get_woe_summary()
            encoded_count = len([f for f, info in woe_summary.items() 
                               if len(info.get('woe_values', [])) > 0])
            total_count = len(woe_summary)
            
            labels = ['已编码', '未编码']
            sizes = [encoded_count, total_count - encoded_count]
            colors = ['lightblue', 'lightgray']
            
            axes[1, 1].pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            axes[1, 1].set_title('WOE编码状态')
        
        plt.tight_layout()
        plt.show()
    
    def save_transformer(self, filepath: str) -> None:
        """保存转换器到文件"""
        import pickle
        
        if not self.fitted:
            raise ValueError("请先调用fit方法拟合转换器")
        
        with open(filepath, 'wb') as f:
            pickle.dump(self, f)
        
        logger.info(f"特征转换器已保存到: {filepath}")
    
    @staticmethod
    def load_transformer(filepath: str) -> 'FeatureTransformer':
        """从文件加载转换器"""
        import pickle
        
        with open(filepath, 'rb') as f:
            transformer = pickle.load(f)
        
        logger.info(f"特征转换器已从文件加载: {filepath}")
        return transformer 