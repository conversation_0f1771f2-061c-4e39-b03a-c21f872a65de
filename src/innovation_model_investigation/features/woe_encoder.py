"""
WOE编码器
将原始特征转换为WOE编码特征
"""

import pandas as pd
import numpy as np
from typing import Dict, Optional, Any
import logging

logger = logging.getLogger(__name__)


class WOEEncoder:
    """WOE编码器"""

    def __init__(self, config=None):
        """
        初始化WOE编码器

        Args:
            config: 配置管理器实例，如果为None则使用默认配置
        """
        self.config = config
        self.woe_mappings = {}
        self.is_fitted = False

        # 从配置中获取WOE编码参数
        if self.config:
            self.woe_config = self.config.get_woe_config()
        else:
            # 默认配置
            self.woe_config = {
                'regularization': 1e-6,
                'handle_unknown': 'error'
            }

        self.regularization = self.woe_config.get('regularization', 1e-6)
        self.handle_unknown = self.woe_config.get('handle_unknown', 'error')
        
    def fit_transform(self, 
                     features: pd.DataFrame, 
                     target: pd.Series,
                     binning_results: Dict) -> pd.DataFrame:
        """
        拟合并转换特征为WOE编码
        
        Args:
            features: 原始特征DataFrame
            target: 目标变量
            binning_results: 分箱结果字典
            
        Returns:
            WOE编码后的特征DataFrame
        """
        logger.info("开始WOE编码转换")
        
        woe_features = pd.DataFrame(index=features.index)
        
        for feature_name in features.columns:
            if feature_name in binning_results:
                woe_values = self._transform_single_feature(
                    features[feature_name], 
                    target,
                    binning_results[feature_name]
                )
                woe_features[feature_name] = woe_values
            else:
                logger.warning(f"特征 {feature_name} 未找到分箱结果，跳过")
                
        # 保存训练时的特征名称，确保transform时特征一致
        self.feature_names_ = list(woe_features.columns)

        self.is_fitted = True
        logger.info(f"WOE编码完成，转换了 {len(woe_features.columns)} 个特征")

        return woe_features
    
    def transform(self, features: pd.DataFrame) -> pd.DataFrame:
        """
        使用已拟合的编码器转换新数据

        Args:
            features: 待转换的特征DataFrame

        Returns:
            WOE编码后的特征DataFrame
        """
        if not self.is_fitted:
            raise ValueError("编码器未拟合，请先调用 fit_transform")

        woe_features = pd.DataFrame(index=features.index)

        # 确保输出特征与训练时完全一致
        # 使用训练时保存的特征列表，而不是woe_mappings的键
        if hasattr(self, 'feature_names_'):
            expected_features = self.feature_names_
        else:
            # 向后兼容：如果没有保存特征名称，使用woe_mappings的键
            expected_features = list(self.woe_mappings.keys())

        for feature_name in expected_features:
            if feature_name in features.columns:
                # 检查特征是否全是NaN值
                if features[feature_name].isna().all():
                    logger.info(f"特征 {feature_name} 全是NaN值，使用0值填充")
                    woe_features[feature_name] = 0.0
                elif feature_name in self.woe_mappings:
                    try:
                        woe_values = self._apply_woe_mapping(
                            features[feature_name],
                            self.woe_mappings[feature_name]
                        )
                        woe_features[feature_name] = woe_values
                    except Exception as e:
                        # 根据handle_unknown配置处理未知值
                        logger.warning(f"特征 {feature_name} 转换时出错，使用0值填充: {e}")
                        woe_features[feature_name] = 0.0
                else:
                    # 特征在训练时存在但没有WOE映射（比如全NaN特征）
                    logger.info(f"特征 {feature_name} 没有WOE映射，使用0值填充")
                    woe_features[feature_name] = 0.0
            else:
                # 如果测试集中缺少训练时的特征，用0填充
                logger.warning(f"测试集中缺少特征 {feature_name}，使用0值填充")
                woe_features[feature_name] = 0.0

        # 检查测试集中是否有训练时没见过的特征
        extra_features = set(features.columns) - set(expected_features)
        if extra_features:
            logger.info(f"测试集包含训练时未见过的特征: {len(extra_features)} 个，将被忽略")

        return woe_features
    
    def _transform_single_feature(self,
                                 feature: pd.Series,
                                 target: pd.Series,
                                 binning_result: Dict) -> pd.Series:
        """转换单个特征为WOE编码"""
        try:
            # 检查特征是否全是NaN值
            if feature.isna().all():
                logger.warning(f"特征 {feature.name} 全是NaN值，返回0值")
                return pd.Series(0.0, index=feature.index, name=feature.name)
                
            # 处理空分箱的情况
            if binning_result.get('is_empty', False):
                logger.warning(f"特征 {feature.name} 是空分箱，返回0值")
                return pd.Series(0.0, index=feature.index, name=feature.name)

            # 使用optbinning实例进行转换
            if 'optb_instance' in binning_result and binning_result['optb_instance'] is not None:
                optb = binning_result['optb_instance']

                # 定义null_mask
                null_mask = feature.isna()

                # 创建结果序列
                woe_series = pd.Series(index=feature.index, name=feature.name)

                # 对非null值进行WOE转换
                if (~null_mask).any():
                    non_null_woe = optb.transform(feature[~null_mask].values, metric="woe")
                    woe_series[~null_mask] = non_null_woe

                # 为null值计算特殊的WOE值
                null_woe = None
                if null_mask.any():
                    # 计算null值的WOE
                    null_woe = self._calculate_null_woe(feature, target, null_mask)
                    woe_series[null_mask] = null_woe

                # 保存WOE映射关系
                self._save_woe_mapping_from_optb(feature, optb, binning_result, null_woe=null_woe)
                
                return woe_series
            else:
                # 使用默认方法计算WOE
                return self._manual_woe_transform(feature, target, binning_result)

        except Exception as e:
            logger.error(f"WOE转换失败 {feature.name}: {e}")
            # 使用手动计算方法作为备选
            return self._manual_woe_transform(feature, target, binning_result)
    
    def _manual_woe_transform(self,
                            feature: pd.Series,
                            target: pd.Series,
                            binning_result: Dict) -> pd.Series:
        """手动计算WOE转换"""
        # 如果全为NaN或（去除NaN后）唯一值，直接返回0
        if feature.isna().all() or feature.nunique(dropna=True) == 1:
            logger.warning(f"特征 {feature.name} 全为NaN或唯一值，WOE无意义，返回0")
            return pd.Series(0.0, index=feature.index, name=feature.name)
        
        splits = binning_result.get('splits', [])

        # 检查是否有NaN值
        null_mask = feature.isna()
        has_nan = null_mask.any()
        
        # 如果没有分割点且没有NaN值，直接返回0
        if (splits is None or len(splits) == 0) and not has_nan:
            logger.warning(f"特征 {feature.name} 没有分割点且没有NaN值，返回0值")
            return pd.Series(0.0, index=feature.index, name=feature.name)
        
        # 即使没有分割点，如果有NaN值，也需要计算WOE
        if splits is None or len(splits) == 0:
            # 如果没有分割点但有NaN值，创建一个特殊的分割点
            valid_feature = feature.dropna()
            if len(valid_feature) > 0:
                # 使用非NaN值的最小值减去一个小值作为分割点
                splits = [valid_feature.min() - 0.1]
            else:
                # 如果全是NaN值，返回0值（已在前面处理，这里冗余，保险起见保留）
                logger.warning(f"特征 {feature.name} 全是NaN值，返回0值")
                return pd.Series(0.0, index=feature.index, name=feature.name)

        # null_mask已在前面定义，这里不需要重复定义
        
        # 创建分箱
        bins = [-np.inf] + list(splits) + [np.inf]
        
        # 创建一个临时特征，用于分箱
        feature_temp = feature.copy()
        
        # 将null值替换为一个特殊值（暂时使用-999）
        feature_temp = feature_temp.fillna(-999)
        
        # 对所有值进行分箱
        feature_binned = pd.cut(feature_temp, bins=bins, include_lowest=True)
        
        # 创建一个映射，记录每个分箱对应的样本是否为null
        is_null = pd.Series(False, index=feature.index)
        is_null[null_mask] = True

        # 计算每个分箱的WOE
        woe_mapping = {}
        total_good = (target == 0).sum()
        total_bad = (target == 1).sum()

        if total_good == 0 or total_bad == 0:
            logger.warning(f"特征 {feature.name} 目标变量分布异常，返回0值")
            return pd.Series(0.0, index=feature.index, name=feature.name)
        
        # 先计算非null值的WOE
        for bin_label in feature_binned.cat.categories:
            # 获取该分箱且非null的样本
            mask = (feature_binned == bin_label) & (~is_null)
            
            if not mask.any():
                continue  # 如果该分箱没有非null样本，跳过
                
            bin_good = (target[mask] == 0).sum()
            bin_bad = (target[mask] == 1).sum()

            if bin_good > 0 and bin_bad > 0:
                good_rate = bin_good / total_good
                bad_rate = bin_bad / total_bad
                woe = np.log(good_rate / bad_rate)
            elif bin_good > 0:
                # 只有好样本，给一个负的WOE值
                woe = -2.0
            elif bin_bad > 0:
                # 只有坏样本，给一个正的WOE值
                woe = 2.0
            else:
                woe = 0.0

            woe_mapping[bin_label] = woe

        # 计算null值的WOE
        if is_null.any():
            null_good = (target[is_null] == 0).sum()
            null_bad = (target[is_null] == 1).sum()
            
            if null_good > 0 and null_bad > 0:
                good_rate = null_good / total_good
                bad_rate = null_bad / total_bad
                null_woe = np.log(good_rate / bad_rate)
                
                # 添加到映射中
                woe_mapping['NULL'] = null_woe
                
                logger.info(f"特征 {feature.name} 的null值WOE: {null_woe:.4f} (好样本: {null_good}, 坏样本: {null_bad})")
            else:
                # 如果null值中没有足够的好坏样本，使用默认值0
                woe_mapping['NULL'] = 0.0
                logger.warning(f"特征 {feature.name} 的null值样本不足，使用默认WOE值0")
        
        # 应用WOE映射
        woe_values = pd.Series(index=feature.index, name=feature.name)
        
        # 为非null值应用WOE
        for bin_label, woe_value in woe_mapping.items():
            if bin_label == 'NULL':
                continue  # 稍后处理null值
                
            mask = (feature_binned == bin_label) & (~is_null)
            woe_values[mask] = woe_value
        
        # 为null值应用WOE
        if 'NULL' in woe_mapping:
            woe_values[is_null] = woe_mapping['NULL']
        
        # 保存映射关系
        self.woe_mappings[feature.name] = {
            'splits': splits,
            'woe_mapping': woe_mapping
        }

        # 处理fillna，将Categorical转换为数值类型
        if hasattr(woe_values, 'cat'):
            # 如果是Categorical类型，先转换为数值
            woe_values = pd.to_numeric(woe_values, errors='coerce')

        return woe_values.fillna(0)
    
    def _calculate_null_woe(self,
                           feature: pd.Series,
                           target: pd.Series,
                           null_mask) -> float:
        """计算null值的WOE"""
        # 获取null值对应的目标变量
        null_target = target[null_mask]
        
        # 计算null值的好坏样本数
        null_good = (null_target == 0).sum()
        null_bad = (null_target == 1).sum()
        
        # 计算总体的好坏样本数
        total_good = (target == 0).sum()
        total_bad = (target == 1).sum()
        
        # 计算WOE值
        if null_good > 0 and null_bad > 0 and total_good > 0 and total_bad > 0:
            # 计算好坏比例
            good_rate = null_good / total_good
            bad_rate = null_bad / total_bad
            
            # 计算WOE
            null_woe = np.log(good_rate / bad_rate)
            
            logger.info(f"特征 {feature.name} 的null值WOE: {null_woe:.4f} (好样本: {null_good}, 坏样本: {null_bad})")
            return null_woe
        else:
            # 如果没有足够的样本，返回0
            logger.warning(f"特征 {feature.name} 的null值样本不足，使用默认WOE值0")
            return 0.0
    
    def _save_woe_mapping_from_optb(self, 
                                   original_feature: pd.Series,
                                   optb_instance,
                                   binning_result: Dict,
                                   null_woe: Optional[float] = None):
        """从optbinning实例保存WOE映射关系"""
        splits = binning_result.get('splits', [])
        
        if splits is None or len(splits) == 0:
            return
            
        # 创建分箱到WOE的映射
        bins = [-np.inf] + list(splits) + [np.inf]
        feature_binned = pd.cut(original_feature, bins=bins, include_lowest=True)
        
        # 使用optbinning计算每个分箱的WOE值
        woe_mapping = {}
        for bin_label in feature_binned.cat.categories:
            mask = feature_binned == bin_label
            if np.any(mask):
                # 获取该分箱内的代表值
                bin_values = original_feature[mask]
                if len(bin_values) > 0:
                    representative_value = bin_values.iloc[0]
                    woe_value = optb_instance.transform(np.array([representative_value]), metric="woe")[0]
                    woe_mapping[bin_label] = woe_value
        
        # 如果有null值的WOE，添加到映射中
        if null_woe is not None:
            woe_mapping["NULL"] = null_woe
        
        self.woe_mappings[original_feature.name] = {
            'splits': splits,
            'woe_mapping': woe_mapping
        }
    
    def _save_woe_mapping(self, 
                         original_feature: pd.Series,
                         woe_feature: pd.Series,
                         binning_result: Dict):
        """保存WOE映射关系"""
        splits = binning_result.get('splits', [])
        
        if splits is None or len(splits) == 0:
            return
            
        # 创建值到WOE的映射
        bins = [-np.inf] + list(splits) + [np.inf]
        feature_binned = pd.cut(original_feature, bins=bins, include_lowest=True)
        
        # 创建分箱到WOE的映射
        woe_mapping = {}
        for bin_label in feature_binned.cat.categories:
            mask = feature_binned == bin_label
            if np.any(mask):
                woe_mapping[bin_label] = woe_feature[mask].iloc[0]
        
        self.woe_mappings[original_feature.name] = {
            'splits': splits,
            'woe_mapping': woe_mapping
        }
    
    def _apply_woe_mapping(self,
                          feature: pd.Series,
                          mapping: Dict) -> pd.Series:
        """应用已保存的WOE映射"""
        splits = mapping['splits']
        woe_mapping = mapping['woe_mapping']

        if splits is None or len(splits) == 0:
            return pd.Series(0.0, index=feature.index, name=feature.name)

        # 先用-999填充NaN值
        feature_filled = feature.fillna(-999)

        # 创建分箱
        bins = [-np.inf] + list(splits) + [np.inf]
        feature_binned = pd.cut(feature_filled, bins=bins, include_lowest=True)

        # 应用WOE映射
        woe_values = feature_binned.map(woe_mapping)

        # 处理fillna，将Categorical转换为数值类型
        if hasattr(woe_values, 'cat'):
            # 如果是Categorical类型，先转换为数值
            woe_values = pd.to_numeric(woe_values, errors='coerce')

        return woe_values.fillna(0)
    
    def get_woe_summary(self) -> Dict[str, Any]:
        """
        获取WOE编码摘要
        
        Returns:
            包含每个特征WOE统计信息的字典
        """
        if not self.woe_mappings:
            logger.warning("未找到WOE映射，返回空摘要")
            return {}
            
        summary_data = {}
        
        for feature_name, mapping in self.woe_mappings.items():
            if 'woe_mapping' in mapping and mapping['woe_mapping']:
                woe_values = list(mapping['woe_mapping'].values())
                summary_data[feature_name] = {
                    'n_bins': len(woe_values),
                    'woe_values': woe_values,
                    'woe_min': min(woe_values),
                    'woe_max': max(woe_values),
                    'woe_range': max(woe_values) - min(woe_values),
                    'woe_std': np.std(woe_values)
                }
        return summary_data
    
    def save_encoder(self, filepath: str):
        """保存编码器到文件"""
        import pickle
        
        encoder_data = {
            'woe_mappings': self.woe_mappings,
            'is_fitted': self.is_fitted
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(encoder_data, f)
            
        logger.info(f"WOE编码器已保存到: {filepath}")
    
    def load_encoder(self, filepath: str):
        """从文件加载编码器"""
        import pickle
        
        with open(filepath, 'rb') as f:
            encoder_data = pickle.load(f)
            
        self.woe_mappings = encoder_data['woe_mappings']
        self.is_fitted = encoder_data['is_fitted']
        
        logger.info(f"WOE编码器已加载自: {filepath}")