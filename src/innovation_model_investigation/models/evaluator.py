"""
模型评估器
负责模型性能评估、指标计算、结果可视化
"""

import pandas as pd
import numpy as np
from typing import Dict,  Tuple, Optional, Any
import logging
from sklearn.metrics import (
    roc_auc_score, roc_curve, precision_recall_curve,
    classification_report, confusion_matrix,
    accuracy_score, precision_score, recall_score, f1_score
)
import matplotlib.pyplot as plt
import seaborn as sns

logger = logging.getLogger(__name__)


class ModelEvaluator:
    """模型评估器"""
    
    def __init__(self):
        self.evaluation_results = {}
        
    def evaluate_binary_classification(self,
                                     model,
                                     X_test: pd.DataFrame,
                                     y_test: pd.Series,
                                     X_train: Optional[pd.DataFrame] = None,
                                     y_train: Optional[pd.Series] = None) -> Dict[str, Any]:
        """
        二分类模型评估
        
        Args:
            model: 训练好的模型
            X_test: 测试特征
            y_test: 测试标签
            X_train: 训练特征（可选，用于计算训练集性能）
            y_train: 训练标签（可选）
            
        Returns:
            评估结果字典
        """
        logger.info("开始模型评估...")
        
        results = {}
        
        # 测试集预测
        y_test_pred = model.predict(X_test)
        y_test_proba = model.predict_proba(X_test)[:, 1]
        
        # 基础分类指标
        test_metrics = self._calculate_classification_metrics(y_test, y_test_pred, y_test_proba)
        results['test_metrics'] = test_metrics
        
        # 训练集指标（如果提供）
        if X_train is not None and y_train is not None:
            y_train_pred = model.predict(X_train)
            y_train_proba = model.predict_proba(X_train)[:, 1]
            train_metrics = self._calculate_classification_metrics(y_train, y_train_pred, y_train_proba)
            results['train_metrics'] = train_metrics
            
            # 过拟合检测
            results['overfitting_analysis'] = {
                'auc_gap': train_metrics['auc'] - test_metrics['auc'],
                'ks_gap': train_metrics['ks'] - test_metrics['ks'],
                'is_overfitting': (train_metrics['auc'] - test_metrics['auc']) > 0.1
            }
        
        # ROC和PR曲线数据
        results['roc_data'] = self._calculate_roc_data(y_test, y_test_proba)
        results['pr_data'] = self._calculate_pr_data(y_test, y_test_proba)
        
        # 混淆矩阵
        results['confusion_matrix'] = confusion_matrix(y_test, y_test_pred)
        
        # 详细分类报告
        results['classification_report'] = classification_report(y_test, y_test_pred, output_dict=True)
        
        # 保存评估结果
        self.evaluation_results = results
        
        logger.info(f"模型评估完成，测试AUC: {test_metrics['auc']:.4f}, KS: {test_metrics['ks']:.4f}")
        
        return results
    
    def _calculate_classification_metrics(self, y_true, y_pred, y_proba) -> Dict[str, float]:
        """计算分类指标"""
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred),
            'recall': recall_score(y_true, y_pred),
            'f1': f1_score(y_true, y_pred),
            'auc': roc_auc_score(y_true, y_proba)
        }
        
        # 计算KS值
        metrics['ks'] = self._calculate_ks_statistic(y_true, y_proba)
        
        return metrics
    
    def _calculate_ks_statistic(self, y_true, y_proba) -> float:
        """计算KS统计量"""
        # 按概率排序
        df = pd.DataFrame({'y_true': y_true, 'y_proba': y_proba})
        df = df.sort_values('y_proba', ascending=False)
        
        # 计算累积分布
        total_pos = df['y_true'].sum()
        total_neg = len(df) - total_pos
        
        df['cum_pos_rate'] = df['y_true'].cumsum() / total_pos
        df['cum_neg_rate'] = (1 - df['y_true']).cumsum() / total_neg
        
        # KS = max(TPR - FPR)
        ks = (df['cum_pos_rate'] - df['cum_neg_rate']).abs().max()
        
        return ks
    
    def _calculate_roc_data(self, y_true, y_proba) -> Dict[str, np.ndarray]:
        """计算ROC曲线数据"""
        fpr, tpr, thresholds = roc_curve(y_true, y_proba)
        return {
            'fpr': fpr,
            'tpr': tpr,
            'thresholds': thresholds,
            'auc': roc_auc_score(y_true, y_proba)
        }
    
    def _calculate_pr_data(self, y_true, y_proba) -> Dict[str, np.ndarray]:
        """计算PR曲线数据"""
        precision, recall, thresholds = precision_recall_curve(y_true, y_proba)
        return {
            'precision': precision,
            'recall': recall,
            'thresholds': thresholds
        }
    
    def plot_evaluation_results(self, figsize: Tuple[int, int] = (15, 10)) -> None:
        """绘制评估结果图表"""
        if not self.evaluation_results:
            logger.error("没有评估结果，请先运行evaluate_binary_classification")
            return
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, axes = plt.subplots(2, 3, figsize=figsize)
        fig.suptitle('模型评估结果', fontsize=16, fontweight='bold')
        
        # 1. ROC曲线
        roc_data = self.evaluation_results['roc_data']
        axes[0, 0].plot(roc_data['fpr'], roc_data['tpr'], 
                       label=f"ROC Curve (AUC = {roc_data['auc']:.3f})", linewidth=2)
        axes[0, 0].plot([0, 1], [0, 1], 'k--', alpha=0.5)
        axes[0, 0].set_xlabel('假正率 (FPR)')
        axes[0, 0].set_ylabel('真正率 (TPR)')
        axes[0, 0].set_title('ROC曲线')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. PR曲线
        pr_data = self.evaluation_results['pr_data']
        axes[0, 1].plot(pr_data['recall'], pr_data['precision'], linewidth=2)
        axes[0, 1].set_xlabel('召回率 (Recall)')
        axes[0, 1].set_ylabel('精确率 (Precision)')
        axes[0, 1].set_title('PR曲线')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 混淆矩阵
        cm = self.evaluation_results['confusion_matrix']
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[0, 2])
        axes[0, 2].set_title('混淆矩阵')
        axes[0, 2].set_xlabel('预测标签')
        axes[0, 2].set_ylabel('真实标签')
        
        # 4. 性能指标对比
        test_metrics = self.evaluation_results['test_metrics']
        metrics_names = ['accuracy', 'precision', 'recall', 'f1', 'auc', 'ks']
        metrics_values = [test_metrics.get(m, 0) for m in metrics_names]
        
        bars = axes[1, 0].bar(metrics_names, metrics_values, color='lightcoral', alpha=0.7)
        axes[1, 0].set_title('性能指标')
        axes[1, 0].set_ylabel('分数')
        axes[1, 0].set_ylim(0, 1)
        
        # 在柱状图上显示数值
        for bar, value in zip(bars, metrics_values):
            height = bar.get_height()
            axes[1, 0].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                           f'{value:.3f}', ha='center', va='bottom')
        
        plt.xticks(rotation=45)
        axes[1, 0].grid(True, alpha=0.3)
        
        # 5. 预测概率分布
        # 这需要额外的数据，暂时跳过
        axes[1, 1].text(0.5, 0.5, '预测概率分布\n(需要额外数据)', 
                       ha='center', va='center', transform=axes[1, 1].transAxes)
        axes[1, 1].set_title('预测概率分布')
        
        # 6. 训练vs测试对比（如果有训练集数据）
        if 'train_metrics' in self.evaluation_results:
            train_metrics = self.evaluation_results['train_metrics']
            
            comparison_metrics = ['auc', 'ks', 'f1']
            train_values = [train_metrics.get(m, 0) for m in comparison_metrics]
            test_values = [test_metrics.get(m, 0) for m in comparison_metrics]
            
            x = np.arange(len(comparison_metrics))
            width = 0.35
            
            axes[1, 2].bar(x - width/2, train_values, width, label='训练集', alpha=0.7)
            axes[1, 2].bar(x + width/2, test_values, width, label='测试集', alpha=0.7)
            
            axes[1, 2].set_xlabel('指标')
            axes[1, 2].set_ylabel('分数')
            axes[1, 2].set_title('训练vs测试性能')
            axes[1, 2].set_xticks(x)
            axes[1, 2].set_xticklabels(comparison_metrics)
            axes[1, 2].legend()
            axes[1, 2].grid(True, alpha=0.3)
        else:
            axes[1, 2].text(0.5, 0.5, '训练vs测试对比\n(无训练集数据)', 
                           ha='center', va='center', transform=axes[1, 2].transAxes)
            axes[1, 2].set_title('训练vs测试性能')
        
        plt.tight_layout()
        plt.show()
    
    def get_evaluation_summary(self) -> str:
        """获取评估摘要报告"""
        if not self.evaluation_results:
            return "没有评估结果，请先运行evaluate_binary_classification"
        
        test_metrics = self.evaluation_results['test_metrics']
        
        summary = f"""
        ========== 模型评估摘要 ==========
        
        📊 测试集性能:
          准确率 (Accuracy): {test_metrics['accuracy']:.4f}
          精确率 (Precision): {test_metrics['precision']:.4f}
          召回率 (Recall): {test_metrics['recall']:.4f}
          F1分数: {test_metrics['f1']:.4f}
          AUC: {test_metrics['auc']:.4f}
          KS值: {test_metrics['ks']:.4f}
        """
        
        # 性能评级
        auc = test_metrics['auc']
        ks = test_metrics['ks']
        
        if auc >= 0.8 and ks >= 0.3:
            performance_grade = "优秀 🌟"
        elif auc >= 0.7 and ks >= 0.2:
            performance_grade = "良好 👍"
        elif auc >= 0.6 and ks >= 0.1:
            performance_grade = "一般 😐"
        else:
            performance_grade = "较差 👎"
        
        summary += f"\n        🎯 性能评级: {performance_grade}"
        
        # 过拟合分析
        if 'overfitting_analysis' in self.evaluation_results:
            overfitting = self.evaluation_results['overfitting_analysis']
            summary += f"""
        
        🔍 过拟合分析:
          AUC差距: {overfitting['auc_gap']:.4f}
          KS差距: {overfitting['ks_gap']:.4f}
          是否过拟合: {'是' if overfitting['is_overfitting'] else '否'}
        """
        
        summary += "\n        ================================="
        
        return summary
    
    def save_evaluation_results(self, filepath: str) -> None:
        """保存评估结果"""
        import json
        import pickle
        
        if not self.evaluation_results:
            logger.error("没有评估结果可保存")
            return
        
        # 准备可序列化的数据
        serializable_results = {}
        for key, value in self.evaluation_results.items():
            if isinstance(value, np.ndarray):
                serializable_results[key] = value.tolist()
            elif isinstance(value, dict):
                serializable_results[key] = {}
                for k, v in value.items():
                    if isinstance(v, np.ndarray):
                        serializable_results[key][k] = v.tolist()
                    else:
                        serializable_results[key][k] = v
            else:
                serializable_results[key] = value
        
        # 保存为JSON
        json_path = filepath.replace('.pkl', '.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        
        # 保存完整对象
        with open(filepath, 'wb') as f:
            pickle.dump(self.evaluation_results, f)
        
        logger.info(f"评估结果已保存到: {filepath} 和 {json_path}") 