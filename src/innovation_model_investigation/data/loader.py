"""
企业风险数据加载器
负责数据加载、基本信息统计和数据概览
"""

import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnterpriseDataLoader:
    """企业风险数据加载器"""
    
    def __init__(self):
        self.data = None
        self.risk_indicators = None
        self.data_dict = None
        
    def load_data(self, data_path: str, dict_path: Optional[str] = None) -> pd.DataFrame:
        """
        加载企业风险样本数据
        
        Args:
            data_path: 样本数据文件路径
            dict_path: 数据字典文件路径
            
        Returns:
            加载的数据DataFrame
        """
        try:
            logger.info(f"正在加载数据: {data_path}")
            self.data = pd.read_csv(data_path)
            
            # 识别风险指标列 - 自动识别所有评分指标
            # 排除非指标列
            exclude_cols = {
                'enterprise_id', 'enterprise_name', 'industry_code', 'region_code',
                'enterprise_size', 'establish_years', 'tech_score', 'tech_adj_score',
                'dev_score', 'dev_adj_score', 'oper_score', 'oper_adj_score',
                'risk_adj_score', 'comprehensive_score', 'innovation_health_level',
                'label', 'data_generate_time'
            }

            self.risk_indicators = [
                col for col in self.data.columns
                if col not in exclude_cols and col.startswith(('tech_', 'dev_', 'oper_', 'risk_'))
            ]
            
            if dict_path:
                logger.info(f"正在加载数据字典: {dict_path}")
                self.data_dict = pd.read_csv(dict_path)
            
            logger.info("数据加载完成")
            return self.data
            
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            raise
    
    def get_data_overview(self) -> Dict:
        """
        获取数据概览信息
        
        Returns:
            包含数据基本信息的字典
        """
        if self.data is None:
            raise ValueError("请先加载数据")
            
        overview = {
            "basic_info": {
                "sample_count": len(self.data),
                "feature_count": self.data.shape[1],
                "risk_indicator_count": len(self.risk_indicators)
            },
            "label_distribution": {
                "good_enterprise": int((self.data['label'] == 0).sum()),
                "bad_enterprise": int((self.data['label'] == 1).sum()),
                "good_ratio": float((self.data['label'] == 0).mean()),
                "bad_ratio": float((self.data['label'] == 1).mean())
            },
            # "categorical_distribution": {
            #     "enterprise_size": self.data['enterprise_size'].value_counts().to_dict(),
            #     "industry_code": self.data['industry_code'].value_counts().to_dict(),
            #     "region_code": self.data['region_code'].value_counts().to_dict()
            # },
            "data_quality": {
                "missing_values": self.data[self.risk_indicators].isnull().sum().sum(),
                "duplicated_rows": int(self.data.duplicated().sum())
            }
        }
        
        return overview
    
    def print_data_summary(self):
        """打印数据摘要信息"""
        overview = self.get_data_overview()
        
        print("=" * 50)
        print("📊 企业风险数据概览")
        print("=" * 50)
        
        # 基本信息
        basic = overview["basic_info"]
        print(f"样本总数: {basic['sample_count']:,}")
        print(f"特征数量: {basic['feature_count']}")
        print(f"风险指标数量: {basic['risk_indicator_count']}")
        print()
        
        # 标签分布
        label = overview["label_distribution"]
        print("🏷️ 标签分布:")
        print(f"  好企业 (label=0): {label['good_enterprise']:,} ({label['good_ratio']:.2%})")
        print(f"  坏企业 (label=1): {label['bad_enterprise']:,} ({label['bad_ratio']:.2%})")
        print()
        
        # 数据质量
        quality = overview["data_quality"]
        print("🔍 数据质量:")
        print(f"  缺失值总数: {quality['missing_values']}")
        print(f"  重复行数: {quality['duplicated_rows']}")
        print()
        
        # # 分类变量分布
        # print("📈 分类变量分布:")
        # for var_name, distribution in overview["categorical_distribution"].items():
        #     print(f"  {var_name}:")
        #     for category, count in distribution.items():
        #         print(f"    {category}: {count}")
        # print()
        
        # 风险指标列表
        print(f"📋 {len(self.risk_indicators)}个科创健康行指标:")
        for i, indicator in enumerate(self.risk_indicators, 1):
            # 从数据字典获取中文名称
            if self.data_dict is not None:
                dict_row = self.data_dict[self.data_dict['indicator_code'] == indicator]
                if not dict_row.empty:
                    cn_name = dict_row.iloc[0]['indicator_name']
                    print(f"  {i:2d}. {indicator} ({cn_name})")
                else:
                    print(f"  {i:2d}. {indicator}")
            else:
                print(f"  {i:2d}. {indicator}")
        
        print("=" * 50)
    
    def get_risk_indicators(self) -> List[str]:
        """获取风险指标列表"""
        if self.risk_indicators is None:
            raise ValueError("请先加载数据")
        return self.risk_indicators.copy()
    
    def get_features_and_target(self) -> Tuple[pd.DataFrame, pd.Series]:
        """
        获取特征矩阵和目标变量
        
        Returns:
            features: 风险指标特征矩阵
            target: 目标变量 (0=好企业, 1=坏企业)
        """
        if self.data is None:
            raise ValueError("请先加载数据")
            
        features = self.data[self.risk_indicators].copy()
        target = self.data['label'].copy()
        
        return features, target
    
    def get_indicator_stats(self) -> pd.DataFrame:
        """
        获取风险指标统计信息
        
        Returns:
            包含各指标统计信息的DataFrame
        """
        if self.data is None:
            raise ValueError("请先加载数据")
            
        stats = self.data[self.risk_indicators].describe()
        
        # 添加标签分组统计
        good_stats = self.data[self.data['label'] == 0][self.risk_indicators].mean()
        bad_stats = self.data[self.data['label'] == 1][self.risk_indicators].mean()
        
        stats.loc['good_enterprise_mean'] = good_stats
        stats.loc['bad_enterprise_mean'] = bad_stats
        stats.loc['mean_difference'] = good_stats - bad_stats
        
        return stats 