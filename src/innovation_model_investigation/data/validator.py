"""
数据质量验证器
负责数据完整性、一致性、准确性检查
"""

import pandas as pd
import numpy as np
from typing import Dict,  Any
import logging

logger = logging.getLogger(__name__)


class DataQualityValidator:
    """数据质量验证器"""
    
    def __init__(self):
        self.validation_results = {}
        
    def validate_data(self, df: pd.DataFrame, target_col: str = 'label') -> Dict[str, Any]:
        """
        完整的数据质量验证
        
        Args:
            df: 待验证的数据
            target_col: 目标变量列名
            
        Returns:
            验证结果字典
        """
        logger.info("开始数据质量验证...")
        
        results = {
            'basic_info': self._validate_basic_info(df),
            'missing_values': self._validate_missing_values(df),
            'data_types': self._validate_data_types(df),
            'target_variable': self._validate_target_variable(df, target_col),
            'feature_quality': self._validate_feature_quality(df, target_col),
            'data_consistency': self._validate_data_consistency(df),
            'outliers': self._detect_outliers(df)
        }
        
        self.validation_results = results
        self._print_validation_summary(results)
        
        return results
    
    def _validate_basic_info(self, df: pd.DataFrame) -> Dict[str, Any]:
        """基本信息验证"""
        return {
            'sample_count': len(df),
            'feature_count': len(df.columns),
            'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024**2,
            'duplicate_rows': df.duplicated().sum(),
            'empty_rows': (df.isnull().all(axis=1)).sum()
        }
    
    def _validate_missing_values(self, df: pd.DataFrame) -> Dict[str, Any]:
        """缺失值验证"""
        missing_stats = df.isnull().sum()
        missing_percent = (missing_stats / len(df) * 100).round(2)
        
        return {
            'total_missing': missing_stats.sum(),
            'missing_by_column': missing_stats.to_dict(),
            'missing_percent_by_column': missing_percent.to_dict(),
            'columns_with_missing': missing_stats[missing_stats > 0].index.tolist(),
            'high_missing_columns': missing_percent[missing_percent > 50].index.tolist()
        }
    
    def _validate_data_types(self, df: pd.DataFrame) -> Dict[str, Any]:
        """数据类型验证"""
        type_info = df.dtypes.value_counts()
        
        return {
            'data_types_summary': type_info.to_dict(),
            'numerical_columns': df.select_dtypes(include=[np.number]).columns.tolist(),
            'categorical_columns': df.select_dtypes(include=['object']).columns.tolist(),
            'datetime_columns': df.select_dtypes(include=['datetime64']).columns.tolist()
        }
    
    def _validate_target_variable(self, df: pd.DataFrame, target_col: str) -> Dict[str, Any]:
        """目标变量验证"""
        if target_col not in df.columns:
            return {'error': f'目标变量 {target_col} 不存在'}
        
        target = df[target_col]
        
        # 检查目标变量类型
        if target.dtype in ['int64', 'float64']:
            # 数值型目标变量
            unique_values = target.nunique()
            if unique_values == 2:
                # 二分类
                class_dist = target.value_counts().to_dict()
                class_balance = min(class_dist.values()) / max(class_dist.values())
                
                return {
                    'type': 'binary_classification',
                    'unique_values': unique_values,
                    'class_distribution': class_dist,
                    'class_balance_ratio': class_balance,
                    'missing_count': target.isnull().sum(),
                    'is_balanced': class_balance >= 0.3
                }
            else:
                # 回归或多分类
                return {
                    'type': 'regression_or_multiclass',
                    'unique_values': unique_values,
                    'min_value': target.min(),
                    'max_value': target.max(),
                    'mean_value': target.mean(),
                    'missing_count': target.isnull().sum()
                }
        else:
            # 分类型目标变量
            class_dist = target.value_counts().to_dict()
            return {
                'type': 'categorical',
                'unique_values': target.nunique(),
                'class_distribution': class_dist,
                'missing_count': target.isnull().sum()
            }
    
    def _validate_feature_quality(self, df: pd.DataFrame, target_col: str) -> Dict[str, Any]:
        """特征质量验证"""
        features = df.drop(columns=[target_col] if target_col in df.columns else [])
        
        quality_issues = {
            'constant_features': [],
            'high_cardinality_features': [],
            'low_variance_features': [],
            'highly_correlated_pairs': []
        }
        
        # 检查常数特征
        for col in features.columns:
            if features[col].nunique() <= 1:
                quality_issues['constant_features'].append(col)
        
        # 检查高基数分类特征
        categorical_cols = features.select_dtypes(include=['object']).columns
        for col in categorical_cols:
            unique_ratio = features[col].nunique() / len(features)
            if unique_ratio > 0.5:
                quality_issues['high_cardinality_features'].append({
                    'column': col,
                    'unique_count': features[col].nunique(),
                    'unique_ratio': unique_ratio
                })
        
        # 检查数值特征的方差
        numerical_cols = features.select_dtypes(include=[np.number]).columns
        for col in numerical_cols:
            if features[col].var() < 0.01:
                quality_issues['low_variance_features'].append(col)
        
        # 检查高相关性特征对
        if len(numerical_cols) > 1:
            corr_matrix = features[numerical_cols].corr().abs()
            # 获取上三角矩阵
            upper_triangle = corr_matrix.where(
                np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
            )
            
            high_corr_pairs = []
            for col in upper_triangle.columns:
                for idx in upper_triangle.index:
                    if upper_triangle.loc[idx, col] > 0.9:
                        high_corr_pairs.append({
                            'feature1': idx,
                            'feature2': col,
                            'correlation': upper_triangle.loc[idx, col]
                        })
            
            quality_issues['highly_correlated_pairs'] = high_corr_pairs
        
        return quality_issues
    
    def _validate_data_consistency(self, df: pd.DataFrame) -> Dict[str, Any]:
        """数据一致性验证"""
        consistency_issues = {
            'negative_values_in_positive_features': [],
            'impossible_ranges': [],
            'format_inconsistencies': []
        }
        
        # 检查应该为正值的特征
        positive_keywords = ['amount', 'count', 'revenue', 'profit', 'asset']
        for col in df.columns:
            if any(keyword in col.lower() for keyword in positive_keywords):
                if df[col].dtype in ['int64', 'float64']:
                    negative_count = (df[col] < 0).sum()
                    if negative_count > 0:
                        consistency_issues['negative_values_in_positive_features'].append({
                            'column': col,
                            'negative_count': negative_count
                        })
        
        # 检查比率特征（应该在0-1之间）
        ratio_keywords = ['ratio', 'rate', 'percentage']
        for col in df.columns:
            if any(keyword in col.lower() for keyword in ratio_keywords):
                if df[col].dtype in ['int64', 'float64']:
                    out_of_range = ((df[col] < 0) | (df[col] > 1)).sum()
                    if out_of_range > 0:
                        consistency_issues['impossible_ranges'].append({
                            'column': col,
                            'out_of_range_count': out_of_range,
                            'min_value': df[col].min(),
                            'max_value': df[col].max()
                        })
        
        return consistency_issues
    
    def _detect_outliers(self, df: pd.DataFrame) -> Dict[str, Any]:
        """异常值检测"""
        numerical_cols = df.select_dtypes(include=[np.number]).columns
        outlier_summary = {}
        
        for col in numerical_cols:
            # IQR方法检测异常值
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = (df[col] < lower_bound) | (df[col] > upper_bound)
            outlier_count = outliers.sum()
            
            if outlier_count > 0:
                outlier_summary[col] = {
                    'outlier_count': outlier_count,
                    'outlier_percentage': (outlier_count / len(df)) * 100,
                    'lower_bound': lower_bound,
                    'upper_bound': upper_bound,
                    'min_outlier': df[col][df[col] < lower_bound].min() if (df[col] < lower_bound).any() else None,
                    'max_outlier': df[col][df[col] > upper_bound].max() if (df[col] > upper_bound).any() else None
                }
        
        return outlier_summary
    
    def _print_validation_summary(self, results: Dict[str, Any]):
        """打印验证结果摘要"""
        print("\n" + "="*60)
        print("📋 数据质量验证报告")
        print("="*60)
        
        # 基本信息
        basic = results['basic_info']
        print(f"\n📊 基本信息:")
        print(f"  样本数量: {basic['sample_count']:,}")
        print(f"  特征数量: {basic['feature_count']}")
        print(f"  内存占用: {basic['memory_usage_mb']:.1f} MB")
        print(f"  重复行数: {basic['duplicate_rows']}")
        
        # 缺失值
        missing = results['missing_values']
        print(f"\n❓ 缺失值情况:")
        print(f"  总缺失值: {missing['total_missing']:,}")
        print(f"  存在缺失值的列: {len(missing['columns_with_missing'])}")
        if missing['high_missing_columns']:
            print(f"  高缺失率列 (>50%): {missing['high_missing_columns']}")
        
        # 目标变量
        target = results['target_variable']
        if 'error' not in target:
            print(f"\n🎯 目标变量:")
            print(f"  类型: {target['type']}")
            if 'class_distribution' in target:
                print(f"  类别分布: {target['class_distribution']}")
                if 'class_balance_ratio' in target:
                    balance_status = "平衡" if target['is_balanced'] else "不平衡"
                    print(f"  类别平衡: {balance_status} (比率: {target['class_balance_ratio']:.3f})")
        
        # 特征质量问题
        quality = results['feature_quality']
        print(f"\n⚠️ 特征质量问题:")
        print(f"  常数特征: {len(quality['constant_features'])}")
        print(f"  高基数特征: {len(quality['high_cardinality_features'])}")
        print(f"  低方差特征: {len(quality['low_variance_features'])}")
        print(f"  高相关性特征对: {len(quality['highly_correlated_pairs'])}")
        
        # 异常值
        outliers = results['outliers']
        if outliers:
            outlier_features = len(outliers)
            total_outliers = sum(info['outlier_count'] for info in outliers.values())
            print(f"\n🔍 异常值检测:")
            print(f"  存在异常值的特征: {outlier_features}")
            print(f"  异常值总数: {total_outliers:,}")
        
        print("\n" + "="*60)
    
    def get_validation_report(self) -> str:
        """生成详细的验证报告"""
        if not self.validation_results:
            return "请先运行 validate_data() 方法"
        
        # 这里可以生成更详细的报告
        # 包括具体的问题列表、建议的修复方案等
        return "详细验证报告生成功能待实现" 