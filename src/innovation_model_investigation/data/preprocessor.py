"""
企业风险数据预处理器
负责数据清洗、缺失值处理、异常值检测等预处理任务
"""

import pandas as pd
import numpy as np
from typing import Dict,  Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class EnterpriseDataPreprocessor:
    """企业风险数据预处理器"""

    def __init__(self, config=None):
        """
        初始化数据预处理器

        Args:
            config: 配置管理器实例，如果为None则使用默认配置
        """
        self.config = config
        self.scalers = {}
        self.encoders = {}
        self.missing_strategies = {}
        self.outlier_bounds = {}

        # 从配置中获取预处理参数
        if self.config:
            self.preprocessing_config = self.config.get_preprocessing_config()
        else:
            # 默认配置
            self.preprocessing_config = {
                'numerical_strategy': 'median',
                'categorical_strategy': 'mode',
                'outlier_method': 'iqr',
                'outlier_factor': 1.5,
                'handle_missing': True,
                'handle_outliers': True
            }
        
    def preprocess_data(self,
                       df: pd.DataFrame,
                       target_col: Optional[str] = None,
                       numerical_strategy: Optional[str] = None,
                       categorical_strategy: Optional[str] = None,
                       outlier_method: Optional[str] = None,
                       outlier_factor: Optional[float] = None,
                       handle_missing: Optional[bool] = None,
                       handle_outliers: Optional[bool] = None) -> Tuple[pd.DataFrame, pd.Series]:
        """
        完整的数据预处理流程

        Args:
            df: 原始数据
            target_col: 目标变量列名，如果为None则使用配置中的值
            numerical_strategy: 数值型缺失值处理策略 ('mean', 'median', 'mode')，如果为None则使用配置中的值
            categorical_strategy: 分类型缺失值处理策略 ('mode', 'unknown')，如果为None则使用配置中的值
            outlier_method: 异常值检测方法 ('iqr', 'zscore')，如果为None则使用配置中的值
            outlier_factor: 异常值检测因子，如果为None则使用配置中的值
            handle_missing: 是否处理缺失值，如果为None则使用配置中的值
            handle_outliers: 是否处理异常值，如果为None则使用配置中的值

        Returns:
            预处理后的特征数据和目标变量
        """
        logger.info("开始数据预处理流程...")

        # 使用配置中的值或传入的参数
        if target_col is None and self.config:
            target_col = self.config.get('data.target_column', 'label')
        elif target_col is None:
            target_col = 'label'

        # 获取预处理参数
        num_strategy = numerical_strategy if numerical_strategy is not None else self.preprocessing_config.get('numerical_strategy', 'median')
        cat_strategy = categorical_strategy if categorical_strategy is not None else self.preprocessing_config.get('categorical_strategy', 'mode')
        out_method = outlier_method if outlier_method is not None else self.preprocessing_config.get('outlier_method', 'iqr')
        out_factor = outlier_factor if outlier_factor is not None else self.preprocessing_config.get('outlier_factor', 1.5)
        do_handle_missing = handle_missing if handle_missing is not None else self.preprocessing_config.get('handle_missing', True)
        do_handle_outliers = handle_outliers if handle_outliers is not None else self.preprocessing_config.get('handle_outliers', True)

        # 分离特征和目标变量
        if target_col in df.columns:
            features = df.drop(columns=[target_col])
            target = df[target_col]
        else:
            features = df.copy()
            target = None

        # 1. 数据类型识别和转换
        features = self._convert_data_types(features)

        # 2. 缺失值处理
        if do_handle_missing:
            features = self._handle_missing_values(
                features,
                numerical_strategy=num_strategy,
                categorical_strategy=cat_strategy
            )

        # 3. 异常值处理
        if do_handle_outliers:
            features = self._handle_outliers(
                features,
                method=out_method,
                factor=out_factor
            )

        # 4. 数据验证
        self._validate_processed_data(features, target)

        logger.info("数据预处理完成")
        return features, target
    
    def _convert_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据类型转换"""
        df_processed = df.copy()
        
        # 识别数值型列
        numerical_cols = df_processed.select_dtypes(include=[np.number]).columns.tolist()
        
        # 识别分类型列
        categorical_cols = df_processed.select_dtypes(include=['object']).columns.tolist()
        
        logger.info(f"识别到 {len(numerical_cols)} 个数值型变量，{len(categorical_cols)} 个分类型变量")
        
        return df_processed
    
    def _handle_missing_values(self,
                              df: pd.DataFrame,
                              numerical_strategy: str = 'median',
                              categorical_strategy: str = 'mode',
                              create_missing_indicators: bool = True) -> pd.DataFrame:
        """
        增强的缺失值处理，支持将缺失值作为有意义的特征

        Args:
            df: 输入数据
            numerical_strategy: 数值型缺失值处理策略
            categorical_strategy: 分类型缺失值处理策略
            create_missing_indicators: 是否为缺失值创建指示变量
        """
        df_processed = df.copy()

        # 统计缺失值
        missing_stats = df_processed.isnull().sum()
        missing_cols = missing_stats[missing_stats > 0]

        if len(missing_cols) > 0:
            logger.info(f"发现 {len(missing_cols)} 个变量存在缺失值")

            for col in missing_cols.index:
                missing_count = missing_cols[col]
                missing_rate = missing_count / len(df_processed)

                if missing_rate > 0.5:
                    logger.warning(f"{col} 缺失率过高 ({missing_rate:.2%})，建议删除")
                    continue

                # 为有意义的缺失值创建指示变量
                if create_missing_indicators and missing_rate > 0.05:  # 缺失率超过5%时创建指示变量
                    indicator_col = f"{col}_is_missing"
                    df_processed[indicator_col] = df_processed[col].isnull().astype(int)
                    logger.info(f"为 {col} 创建缺失值指示变量: {indicator_col}")

                # 根据数据类型选择填充策略
                if df_processed[col].dtype in ['int64', 'float64']:
                    # 数值型变量
                    if numerical_strategy == 'mean':
                        fill_value = df_processed[col].mean()
                    elif numerical_strategy == 'median':
                        fill_value = df_processed[col].median()
                    elif numerical_strategy == 'mode':
                        fill_value = df_processed[col].mode().iloc[0] if not df_processed[col].mode().empty else 0
                    elif numerical_strategy == 'special_value':
                        # 使用特殊值（如-999）来标识缺失
                        fill_value = -999
                    else:
                        fill_value = 0

                    df_processed[col] = df_processed[col].fillna(fill_value)
                    self.missing_strategies[col] = {'type': 'numerical', 'strategy': numerical_strategy, 'value': fill_value}

                else:
                    # 分类型变量
                    if categorical_strategy == 'mode':
                        fill_value = df_processed[col].mode().iloc[0] if not df_processed[col].mode().empty else 'unknown'
                    elif categorical_strategy == 'missing_category':
                        # 保留缺失值作为单独的类别
                        fill_value = 'MISSING_VALUE'
                    else:
                        fill_value = 'unknown'

                    df_processed[col] = df_processed[col].fillna(fill_value)
                    self.missing_strategies[col] = {'type': 'categorical', 'strategy': categorical_strategy, 'value': fill_value}

                logger.info(f"{col}: 填充 {missing_count} 个缺失值，策略: {fill_value}")

        return df_processed
    
    def _handle_outliers(self, 
                        df: pd.DataFrame,
                        method: str = 'iqr',
                        factor: float = 1.5) -> pd.DataFrame:
        """异常值处理"""
        df_processed = df.copy()
        
        # 只对数值型变量处理异常值
        numerical_cols = df_processed.select_dtypes(include=[np.number]).columns.tolist()
        
        for col in numerical_cols:
            if method == 'iqr':
                # IQR方法
                Q1 = df_processed[col].quantile(0.25)
                Q3 = df_processed[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - factor * IQR
                upper_bound = Q3 + factor * IQR
                
            elif method == 'zscore':
                # Z-score方法
                mean = df_processed[col].mean()
                std = df_processed[col].std()
                lower_bound = mean - factor * std
                upper_bound = mean + factor * std
                
            else:
                continue
            
            # 统计异常值
            outliers = (df_processed[col] < lower_bound) | (df_processed[col] > upper_bound)
            outlier_count = outliers.sum()
            
            if outlier_count > 0:
                # 使用边界值替换异常值
                df_processed.loc[df_processed[col] < lower_bound, col] = lower_bound
                df_processed.loc[df_processed[col] > upper_bound, col] = upper_bound
                
                self.outlier_bounds[col] = {
                    'method': method,
                    'lower_bound': lower_bound,
                    'upper_bound': upper_bound,
                    'outlier_count': outlier_count
                }
                
                logger.info(f"{col}: 处理 {outlier_count} 个异常值 (范围: [{lower_bound:.2f}, {upper_bound:.2f}])")
        
        return df_processed
    
    def _validate_processed_data(self, features: pd.DataFrame, target: Optional[pd.Series] = None):
        """数据验证"""
        # 检查缺失值
        missing_count = features.isnull().sum().sum()
        if missing_count > 0:
            logger.warning(f"预处理后仍有 {missing_count} 个缺失值")
        
        # 检查数据类型
        logger.info(f"预处理完成: {features.shape[0]} 样本, {features.shape[1]} 特征")
        
        # 检查目标变量
        if target is not None:
            target_missing = target.isnull().sum()
            if target_missing > 0:
                logger.warning(f"目标变量存在 {target_missing} 个缺失值")
            
            # 检查标签分布
            label_dist = target.value_counts()
            logger.info(f"标签分布: {dict(label_dist)}")
    
    def get_preprocessing_summary(self) -> Dict:
        """获取预处理摘要信息"""
        return {
            'missing_strategies': self.missing_strategies,
            'outlier_bounds': self.outlier_bounds,
            'scalers': list(self.scalers.keys()),
            'encoders': list(self.encoders.keys())
        }
    
    def apply_preprocessing(self, df: pd.DataFrame) -> pd.DataFrame:
        """对新数据应用相同的预处理策略"""
        df_processed = df.copy()
        
        # 应用缺失值处理策略
        for col, strategy in self.missing_strategies.items():
            if col in df_processed.columns:
                df_processed[col].fillna(strategy['value'], inplace=True)
        
        # 应用异常值处理
        for col, bounds in self.outlier_bounds.items():
            if col in df_processed.columns:
                df_processed.loc[df_processed[col] < bounds['lower_bound'], col] = bounds['lower_bound']
                df_processed.loc[df_processed[col] > bounds['upper_bound'], col] = bounds['upper_bound']
        
        return df_processed 