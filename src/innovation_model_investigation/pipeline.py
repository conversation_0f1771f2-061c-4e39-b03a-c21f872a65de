"""
企业风险评估模型端到端主流程脚本
本脚本实现了数据加载、特征工程、分箱、WOE编码、模型训练、评估与输出的完整自动化流程。
适合生产环境或主流程调用，不建议仅作为演示用例。
"""

import sys
import warnings
from pathlib import Path
import toml

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))
warnings.filterwarnings("ignore")

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split

# 导入项目模块
from innovation_model_investigation.data.loader import EnterpriseDataLoader
from innovation_model_investigation.data.preprocessor import EnterpriseDataPreprocessor
from innovation_model_investigation.features.binning import OptimalBinning
from innovation_model_investigation.features.woe_encoder import WOEEncoder
from innovation_model_investigation.models.scorecard import ScorecardModel
from innovation_model_investigation.models.trainer import ModelTrainer
from innovation_model_investigation.utils.metrics import ModelMetrics
from innovation_model_investigation.utils.config import Config

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False


def get_project_version(pyproject_path="pyproject.toml"):
    data = toml.load(pyproject_path)
    return data["project"]["version"]


def main(version: str, sample_data_path: str):
    """
    主流程函数

    Args:
        version: 可选的版本号，如果不提供则从 pyproject.toml 读取
    """
    print("🚀 开始企业风险评估模型权重优化流水线")
    print("=" * 60)

    # ========== 0. 配置初始化 ==========
    print("\n⚙️ 步骤0: 配置初始化")
    print("-" * 30)

    # 初始化配置管理器
    config = Config()
    print("✅ 配置管理器初始化完成")

    # 打印关键配置信息
    print(
        f"数据配置: 目标列={config.get('data.target_column')}, 测试集比例={config.get('data.test_size')}"
    )
    print(
        f"分箱配置: 最小分箱数={config.get('binning.min_n_bins')}, 最大分箱数={config.get('binning.max_n_bins')}"
    )
    print(
        f"模型配置: 算法={config.get('model.algorithm')}, 交叉验证折数={config.get('model.cv_folds')}"
    )

    # ========== 1. 数据加载与探索 ==========
    print("\n📥 步骤1: 数据加载与探索")
    print("-" * 30)

    # 初始化数据加载器
    loader = EnterpriseDataLoader()

    # 从配置中获取数据路径
    data_path = sample_data_path or config.get(
        "data.sample_data_path", "data/enterprise_risk_sample_data.csv"
    )
    dict_path = config.get("data.data_dict_path", "data/data_dictionary.csv")

    data = loader.load_data(data_path, dict_path)

    # 打印数据概览
    loader.print_data_summary()

    # 获取特征和目标变量
    features, target = loader.get_features_and_target()

    print(
        f"✅ 数据加载完成，共 {len(features)} 个样本，{len(features.columns)} 个风险指标"
    )

    # ========== 2. 数据分割 ==========
    print("\n✂️ 步骤2: 数据分割")
    print("-" * 30)

    # 从配置中获取分割参数
    test_size = config.get("data.test_size", 0.3)
    random_state = config.get("data.random_state", 42)

    X_train, X_test, y_train, y_test = train_test_split(
        features,
        target,
        test_size=test_size,
        random_state=random_state,
        stratify=target,
    )

    print(
        f"训练集: {len(X_train)} 样本 (好企业: {(y_train==0).sum()}, 坏企业: {(y_train==1).sum()})"
    )
    print(
        f"测试集: {len(X_test)} 样本 (好企业: {(y_test==0).sum()}, 坏企业: {(y_test==1).sum()})"
    )

    # ========== 3. 最优分箱 ==========
    print("\n🔄 步骤3: 最优分箱")
    print("-" * 30)

    # 初始化分箱器，传入配置
    binning = OptimalBinning(config=config)

    # 执行分箱
    binning_results = binning.fit_transform_all(X_train, y_train)

    # 查看IV值排序
    iv_ranking = binning.get_iv_ranking()
    print("\n📊 特征重要性排序 (Top 10):")
    print(iv_ranking.head(10))

    # 绘制分箱汇总图
    # print("\n🎨 生成分箱可视化图表...")
    # fig = binning.plot_binning_summary()

    # ========== 4. WOE编码 ==========
    print("\n🔢 步骤4: WOE编码转换")
    print("-" * 30)

    # 初始化WOE编码器，传入配置
    woe_encoder = WOEEncoder(config=config)

    # 对训练集进行WOE编码
    X_train_woe = woe_encoder.fit_transform(X_train, y_train, binning_results)

    # 对测试集进行WOE编码
    X_test_woe = woe_encoder.transform(X_test)

    print(
        f"✅ WOE编码完成，训练集形状: {X_train_woe.shape}, 测试集形状: {X_test_woe.shape}"
    )

    # 查看WOE编码摘要
    woe_summary = woe_encoder.get_woe_summary()
    print("\n📋 WOE编码摘要 (Top 10):")

    if woe_summary:
        # 转换为DataFrame用于显示
        summary_data = []
        for feature_name, info in woe_summary.items():
            summary_data.append(
                {
                    "feature_name": feature_name,
                    "n_bins": info["n_bins"],
                    "woe_min": info["woe_min"],
                    "woe_max": info["woe_max"],
                    "woe_range": info["woe_range"],
                    "woe_std": info["woe_std"],
                }
            )

        woe_df = pd.DataFrame(summary_data)
        print(woe_df.head(10))
    else:
        print("暂无WOE编码摘要")

    # ========== 5. 模型训练 ==========
    print("\n🎯 步骤5: 逻辑回归模型训练")
    print("-" * 30)

    # 从配置中获取模型参数
    model_config = config.get_model_config()
    random_state = config.get("data.random_state", 42)

    # 初始化评分卡模型，使用配置中的参数
    scorecard = ScorecardModel(
        regularization="l1", C=1.0, random_state=random_state  # L1正则化进行特征选择
    )

    # 训练模型
    scorecard.fit(X_train_woe, y_train)

    # 获取特征权重
    feature_weights = scorecard.get_feature_weights()

    print("\n⚖️ 学习到的特征权重 (Top 15):")
    print(feature_weights.head(15)[["feature_name", "coefficient", "importance_rank"]])

    # ========== 6. 模型评估 ==========
    print("\n📊 步骤6: 模型效果评估")
    print("-" * 30)

    # 在测试集上预测
    y_pred_proba = scorecard.predict_proba(X_test_woe)[:, 1]  # 坏企业概率
    y_pred = scorecard.predict(X_test_woe)

    # 综合评估
    evaluation_results = ModelMetrics.comprehensive_evaluation(
        y_test.values, y_pred, y_pred_proba
    )

    # 打印评估结果
    ModelMetrics.print_evaluation_summary(evaluation_results)

    # ========== 7. 交叉验证 ==========
    print("\n🔄 步骤7: 交叉验证")
    print("-" * 30)

    # 从配置中获取交叉验证折数
    cv_folds = config.get("model.cv_folds", 5)
    cv_results = scorecard.cross_validate(X_train_woe, y_train, cv=cv_folds)

    print(f"📈 {cv_folds}折交叉验证结果:")
    for metric, results in cv_results.items():
        print(f"  {metric.upper()}: {results['mean']:.4f} (±{results['std']:.4f})")

    # ========== 8. 评分卡应用 ==========
    print("\n🏆 步骤8: 评分卡分数计算")
    print("-" * 30)

    # 从配置中获取评分卡参数
    scorecard_config = config.get("scorecard_parameters", {})
    base_score = scorecard_config.get("base_score", 600)
    pdo = scorecard_config.get("pdo", 20)

    # 计算评分卡分数
    test_scores = scorecard.calculate_score(X_test_woe, base_score=base_score, pdo=pdo)

    # 分析分数分布
    print(f"评分卡分数统计:")
    print(f"  最低分: {test_scores.min():.0f}")
    print(f"  最高分: {test_scores.max():.0f}")
    print(f"  平均分: {test_scores.mean():.0f}")
    print(f"  中位分: {test_scores.median():.0f}")

    # 按好坏企业分析分数
    good_scores = test_scores[y_test == 0]
    bad_scores = test_scores[y_test == 1]

    print(f"\n好企业平均分: {good_scores.mean():.0f}")
    print(f"坏企业平均分: {bad_scores.mean():.0f}")
    print(f"分数区分度: {good_scores.mean() - bad_scores.mean():.0f}")

    # ========== 9. 与传统权重对比 ==========
    print("\n⚡ 步骤9: 与传统权重体系对比")
    print("-" * 30)

    # 加载原始数据中的综合评分作为传统方法基准
    if "comprehensive_score" in data.columns:
        traditional_scores = data.loc[X_test.index, "comprehensive_score"]
        # 综合评分越高风险越低，需要转换为风险概率（取负值）
        traditional_risk_scores = -traditional_scores

        # 新增：填充NaN为0，避免AUC报错
        traditional_risk_scores = traditional_risk_scores.fillna(0)

        # 计算传统方法的AUC
        traditional_auc = ModelMetrics.calculate_auc(
            y_test.values, traditional_risk_scores.values
        )
        model_auc = evaluation_results["classification_metrics"]["auc"]

        print(f"传统综合评分方法AUC: {traditional_auc:.4f}")
        print(f"机器学习方法AUC:     {model_auc:.4f}")
        print(
            f"AUC提升幅度:          {model_auc - traditional_auc:.4f} ({(model_auc/traditional_auc-1)*100:.1f}%)"
        )
    else:
        print("⚠️ 未找到传统评分字段，跳过对比分析")

    # ========== 10. 权重对比分析 ==========
    print("\n🔍 步骤10: 权重体系对比分析")
    print("-" * 30)

    # 显示权重变化最大的指标
    print("权重变化显著的指标:")
    significant_features = feature_weights[feature_weights["abs_coefficient"] > 0.1]

    for _, row in significant_features.head(10).iterrows():
        feature_name = row["feature_name"]
        coef = row["coefficient"]

        # 从数据字典获取中文名称
        if loader.data_dict is not None:
            dict_row = loader.data_dict[
                loader.data_dict["indicator_code"] == feature_name
            ]
            if not dict_row.empty:
                cn_name = dict_row.iloc[0]["indicator_name"]
                print(f"  {feature_name} ({cn_name}): 权重系数 = {coef:.4f}")
            else:
                print(f"  {feature_name}: 权重系数 = {coef:.4f}")
        else:
            print(f"  {feature_name}: 权重系数 = {coef:.4f}")

    # ========== 11. 保存结果 ==========
    print("\n💾 步骤11: 保存模型和结果")
    print("-" * 30)

    # 获取版本号，创建输出目录
    if version is None:
        version = get_project_version()
        print(f"📋 使用 pyproject.toml 版本: {version}")
    else:
        print(f"📋 使用指定版本: {version}")

    output_dir = Path(f"outputs/{version}")
    output_dir.mkdir(parents=True, exist_ok=True)

    # 保存模型组件
    scorecard.save_model(output_dir / "models_scorecard_model.pkl")
    woe_encoder.save_encoder(output_dir / "models_woe_encoder.pkl")
    binning.save_binning_results(output_dir / "binning_binning_results.pkl")

    # 保存权重结果
    feature_weights.to_csv(
        output_dir / "feature_feature_weights.csv", index=False, encoding="utf-8"
    )
    iv_ranking.to_csv(
        output_dir / "binning_iv_ranking.csv", index=False, encoding="utf-8"
    )

    # 保存评估结果
    import json
    from innovation_model_investigation.api.utils.rustfs_storage import make_serializable

    serializable_results = make_serializable(evaluation_results)
    with open(output_dir / "evaluation_results.json", "w", encoding="utf-8") as f:
        json.dump(serializable_results, f, ensure_ascii=False, indent=2)

    # ========== 12. 输出全量样本分数对比表 ==========
    print("\n📤 步骤12: 输出全量样本分数对比表")
    # 对全量原始数据做WOE编码和模型打分
    all_features, all_target = loader.get_features_and_target()

    all_woe = woe_encoder.transform(all_features)
    all_model_score = scorecard.predict_proba(all_woe)[:, 1]
    compare_df = all_features.copy()
    if "enterprise_id" in data.columns:
        compare_df["enterprise_id"] = data["enterprise_id"]
    compare_df["traditional_total_score"] = data["comprehensive_score"]
    compare_df["model_score"] = all_model_score
    compare_df["label"] = all_target.values
    # 增加 is_test 列
    compare_df["is_test"] = 0
    compare_df.loc[X_test.index, "is_test"] = 1
    compare_df.to_csv(
        output_dir / "evaluation_model_scorecard_compare.csv",
        index=False,
        encoding="utf-8",
    )
    print(
        f"✅ 全量分数对比表已保存到 {output_dir}/evaluation_model_scorecard_compare.csv"
    )

    # ========== 新增: 生成完整的 baseline 对比信息 ==========
    print("\n📊 生成完整的 baseline 对比信息...")
    (output_dir / "baseline").mkdir(exist_ok=True)

    # 1. 整合所有对比相关数据到 baseline_performance.json
    baseline_metrics = {
        "version": version,
        "generated_time": pd.Timestamp.now().isoformat(),
        "model_performance": {
            "train_auc": evaluation_results["classification_metrics"]["auc"],
            "test_auc": model_auc,
            "test_ks": evaluation_results["classification_metrics"]["ks"],
            "test_accuracy": evaluation_results["classification_metrics"]["accuracy"],
            "test_precision": evaluation_results["classification_metrics"]["precision"],
            "test_recall": evaluation_results["classification_metrics"]["recall"],
            "test_f1": evaluation_results["classification_metrics"]["f1_score"],
        },
        "traditional_performance": {
            "auc": traditional_auc,
            "improvement_over_traditional": model_auc - traditional_auc,
            "improvement_pct": (
                (model_auc / traditional_auc - 1) * 100 if traditional_auc > 0 else 0
            ),
        },
        "data_summary": {
            "total_samples": len(all_features),
            "train_samples": len(X_train),
            "test_samples": len(X_test),
            "positive_rate": float(all_target.mean()),
            "feature_count": len(feature_weights),
        },
        # 新增：整合 evaluation_results.json 的关键信息
        "detailed_evaluation": {
            "confusion_matrix": evaluation_results["confusion_matrix"],
            "classification_report": evaluation_results.get(
                "classification_report", {}
            ),
            "feature_importance": feature_weights.head(10)[
                ["feature_name", "coefficient", "abs_coefficient"]
            ].to_dict("records"),
        },
        # 新增：数据质量指标
        "data_quality": {
            "iv_above_0_1": int((iv_ranking["iv"] > 0.1).sum()),
            "iv_above_0_3": int((iv_ranking["iv"] > 0.3).sum()),
            "total_features": len(iv_ranking),
            "avg_iv": float(iv_ranking["iv"].mean()),
        },
    }

    with open(
        output_dir / "baseline_baseline_performance.json", "w", encoding="utf-8"
    ) as f:
        json.dump(baseline_metrics, f, ensure_ascii=False, indent=2)

    print(
        f"✅ 完整baseline性能指标已保存到 {output_dir}/baseline/baseline_performance.json"
    )

    # 2. 生成详细的对比分析结果
    print("\n📊 生成详细对比分析...")

    # 分析不同数据集的表现
    subset_analysis = {}
    for subset, subset_name in zip(
        ["all", "train", "test"], ["全量样本", "训练集", "测试集"]
    ):
        if subset == "test":
            df_sub = (
                compare_df[compare_df["is_test"] == 1]
                if "is_test" in compare_df.columns
                else compare_df
            )
        elif subset == "train":
            df_sub = (
                compare_df[compare_df["is_test"] == 0]
                if "is_test" in compare_df.columns
                else compare_df
            )
        else:
            df_sub = compare_df

        if len(df_sub) > 0:
            y_true = df_sub["label"]
            traditional_score = -df_sub["traditional_total_score"]
            model_score = df_sub["model_score"]

            # 新增：同步去除三者中有NaN的样本，避免AUC报错
            valid_mask = (
                (~y_true.isna()) & (~traditional_score.isna()) & (~model_score.isna())
            )
            y_true_valid = y_true[valid_mask]
            traditional_score_valid = traditional_score[valid_mask]
            model_score_valid = model_score[valid_mask]

            # 计算传统方法性能
            from sklearn.metrics import roc_auc_score

            if len(y_true_valid) > 0:
                trad_auc = roc_auc_score(y_true_valid, traditional_score_valid)
                model_auc_subset = roc_auc_score(y_true_valid, model_score_valid)
            else:
                trad_auc = None
                model_auc_subset = None

            subset_analysis[subset] = {
                "subset_name": subset_name,
                "sample_count": len(df_sub),
                "traditional_auc": float(trad_auc) if trad_auc is not None else None,
                "model_auc": (
                    float(model_auc_subset) if model_auc_subset is not None else None
                ),
                "auc_improvement": (
                    float(model_auc_subset - trad_auc)
                    if (trad_auc is not None and model_auc_subset is not None)
                    else None
                ),
                "improvement_pct": (
                    float((model_auc_subset / trad_auc - 1) * 100)
                    if (
                        trad_auc is not None
                        and trad_auc > 0
                        and model_auc_subset is not None
                    )
                    else None
                ),
            }

    # 3. 保存完整的对比分析结果
    comparison_results = {
        "version": version,
        "generated_time": pd.Timestamp.now().isoformat(),
        "overall_comparison": {
            "model_auc": float(model_auc),
            "traditional_auc": float(traditional_auc),
            "improvement": float(model_auc - traditional_auc),
            "improvement_pct": (
                float((model_auc / traditional_auc - 1) * 100)
                if traditional_auc > 0
                else 0
            ),
            "winner": "model" if model_auc > traditional_auc else "traditional",
        },
        "subset_analysis": subset_analysis,
        "data_sources": {
            "evaluation_results": "evaluation/evaluation_results.json",
            "compare_data": "evaluation/model_scorecard_compare.csv",
            "feature_weights": "feature/feature_weights.csv",
            "iv_ranking": "binning/iv_ranking.csv",
        },
        "usage_note": "此文件整合了所有对比相关数据，供对比脚本使用",
    }

    with open(
        output_dir / "baseline_comparison_results.json", "w", encoding="utf-8"
    ) as f:
        json.dump(comparison_results, f, ensure_ascii=False, indent=2)

    print(f"✅ 详细对比分析已保存到 {output_dir}/baseline/comparison_results.json")

    # 4. 复制对比数据到 baseline 目录（便于对比脚本使用）
    import shutil

    # 复制关键对比数据到根目录，添加前缀
    baseline_compare_path = output_dir / "baseline_model_scorecard_compare.csv"
    shutil.copy2(
        output_dir / "evaluation_model_scorecard_compare.csv", baseline_compare_path
    )
    print(f"✅ 对比数据已复制到 {output_dir}/baseline_model_scorecard_compare.csv")

    print(f"✅ 所有结果已保存到 {output_dir} 目录")
    print(f"📊 baseline 对比数据已整合到 {output_dir} 目录")
    print("\n🎉 权重优化流水线执行完成!")
    print("=" * 60)

    return {
        "scorecard": scorecard,
        "woe_encoder": woe_encoder,
        "binning": binning,
        "feature_weights": feature_weights,
        "evaluation_results": evaluation_results,
        "output_dir": output_dir,
    }


if __name__ == "__main__":
    # 执行完整流水线
    results = main()

    # 可以继续进行更多分析...
    print("\n💡 提示: 您可以继续使用返回的对象进行更多分析:")
    print("  - results['scorecard']: 训练好的评分卡模型")
    print("  - results['feature_weights']: 学习到的特征权重")
    print("  - results['evaluation_results']: 详细评估结果")
