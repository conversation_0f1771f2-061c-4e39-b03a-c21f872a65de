"""
训练服务，集成pipeline和RustFS存储
"""

import time
import json
import uuid
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, Tuple, Optional
import pandas as pd
from loguru import logger

from innovation_model_investigation.api.services.core_training import CoreTrainingEngine
from innovation_model_investigation.api.utils.rustfs_storage import RustFSModelStorage
from innovation_model_investigation.api.utils.data_client import DataClient
from innovation_model_investigation.pipeline import get_project_version


class TrainingService:
    """训练服务"""

    def __init__(
        self,
        models_dir: str = "outputs",
        web_platform_url: Optional[str] = None,
        api_key: Optional[str] = None,
    ):
        """
        初始化训练服务

        Args:
            models_dir: 模型存储目录
            web_platform_url: Web平台API基础URL
            api_key: API密钥
        """
        # 使用RustFS存储
        self.rustfs_storage = RustFSModelStorage(base_dir=models_dir)

        # 初始化数据客户端
        self.data_client = DataClient(
            base_url=web_platform_url or "", api_key=api_key or ""
        )

        # 创建输出目录
        self.output_dir = Path(models_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 初始化核心训练引擎
        self.core_engine = CoreTrainingEngine()

    async def train_model(
        self, traningSampleDataPath: str, modelVersion: str
    ) -> Tuple[str, Dict[str, Any]]:
        """
        执行模型训练（使用pipeline流程并保存到RustFS）

        Args:
            traningSampleDataPath: 训练样本数据路径（rustfs路径）
            modelVersion: 模型版本

        Returns:
            模型ID和训练结果
        """
        logger.info(
            f"🚀 开始训练模型: 数据路径={traningSampleDataPath}, 版本={modelVersion}"
        )
        start_time = time.time()

        try:
            # 从rustfs下载数据到本地临时目录
            local_sample_data_path = await self._download_sample_data_from_rustfs(
                traningSampleDataPath
            )

            # 使用核心训练引擎执行pipeline训练
            version, result = await self.core_engine.execute_training_with_pipeline(
                version=modelVersion,
                sample_data_path=local_sample_data_path,
            )

            # 生成模型ID
            model_id = f"model_{uuid.uuid4().hex[:8]}"

            # 保存到RustFS
            logger.info("💾 开始保存到RustFS...")
            self.rustfs_storage.save_model_from_outputs(version=version)

            # 记录训练日志
            training_log = {
                "timestamp": time.time(),
                "traningSampleDataPath": traningSampleDataPath,
                "modelVersion": modelVersion,
                "model_id": model_id,
                "version": version,
                "metrics": result["metrics"],
                "training_time": result["training_time"],
            }

            # 确保日志目录存在
            log_dir = Path("logs/training")
            log_dir.mkdir(parents=True, exist_ok=True)

            # 写入日志文件
            log_file = log_dir / f"training_{time.strftime('%Y%m%d')}.jsonl"
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(training_log, ensure_ascii=False) + "\n")

            logger.info(
                f"✅ 模型训练完成: 模型ID={model_id}, 版本={version}, 耗时={result['training_time']:.2f}秒"
            )

            return model_id, {
                "version": version,
                "metrics": result["metrics"],
                "training_time": result["training_time"],
            }

        except Exception as e:
            logger.exception(f"❌ 模型训练失败: {str(e)}")
            raise
        finally:
            # 清理临时文件
            if "local_sample_data_path" in locals():
                self._cleanup_temp_file(local_sample_data_path)

    async def _download_sample_data_from_rustfs(self, rustfs_path: str) -> str:
        """
        从rustfs下载样本数据到本地临时目录

        Args:
            rustfs_path: rustfs中的文件路径

        Returns:
            本地临时文件路径
        """
        try:
            logger.info(f"📥 从rustfs下载数据: {rustfs_path}")

            # 创建临时目录
            temp_dir = Path(tempfile.mkdtemp(prefix="training_data_"))
            local_file_path = temp_dir / "sample_data.csv"

            # 从rustfs下载文件
            if hasattr(self.rustfs_storage, "download_file"):
                await self.rustfs_storage.download_file(
                    rustfs_path, str(local_file_path)
                )
            else:
                # 如果没有download_file方法，尝试其他方式
                logger.warning("⚠️ RustFS没有download_file方法，尝试其他方式")
                # 这里可以添加其他下载逻辑
                raise NotImplementedError("RustFS下载功能未实现")

            logger.info(f"✅ 数据下载完成: {local_file_path}")
            return str(local_file_path)

        except Exception as e:
            logger.exception(f"❌ 从rustfs下载数据失败: {str(e)}")
            raise

    def _cleanup_temp_file(self, file_path: str):
        """
        清理临时文件

        Args:
            file_path: 临时文件路径
        """
        try:
            temp_path = Path(file_path)
            if temp_path.exists():
                # 删除文件
                temp_path.unlink()
                # 删除父目录
                temp_path.parent.rmdir()
                logger.info(f"🧹 清理临时文件: {file_path}")
        except Exception as e:
            logger.warning(f"⚠️ 清理临时文件失败: {str(e)}")

    async def fetch_training_data(self, training_set_id: str) -> pd.DataFrame:
        """
        获取训练数据

        Args:
            training_set_id: 训练集ID

        Returns:
            训练数据DataFrame
        """
        try:
            # 尝试从Web平台获取数据
            if hasattr(self.data_client, "get_dataset"):
                data = await self.data_client.get_dataset(training_set_id)
                logger.info(f"📊 从Web平台获取训练数据: {len(data)} 行")
                return data
            else:
                logger.warning("⚠️ DataClient没有get_dataset方法")
                raise NotImplementedError("DataClient.get_dataset方法未实现")
        except Exception as e:
            logger.warning(f"⚠️ 从Web平台获取数据失败: {str(e)}")

            # 回退到本地数据文件
            local_data_path = Path("data/enterprise_risk_sample_data.csv")
            if local_data_path.exists():
                data = pd.read_csv(local_data_path)
                logger.info(f"📊 使用本地数据文件: {len(data)} 行")
                return data
            else:
                raise FileNotFoundError(
                    "无法获取训练数据：Web平台不可用且本地数据文件不存在"
                )

    def list_models(self) -> list:
        """
        列出所有模型

        Returns:
            模型列表
        """
        try:
            return self.rustfs_storage.list_models()
        except Exception as e:
            logger.exception(f"❌ 获取模型列表失败: {str(e)}")
            return []

    def get_model_info(self, model_id: str) -> Dict[str, Any]:
        """
        获取模型信息

        Args:
            model_id: 模型ID

        Returns:
            模型信息
        """
        try:
            # 从模型列表中查找对应的版本
            models = self.list_models()
            for model in models:
                if model.get("model_id") == model_id:
                    version = model.get("version")
                    if hasattr(self.rustfs_storage, "get_model_info"):
                        model_info = self.rustfs_storage.get_model_info(version)
                        if model_info is not None:
                            return model_info
                        else:
                            # 如果get_model_info返回None，返回模型信息
                            return model
                    else:
                        # 如果没有get_model_info方法，返回模型信息
                        return model

            raise FileNotFoundError(f"模型不存在: {model_id}")

        except Exception as e:
            logger.exception(f"❌ 获取模型信息失败: {str(e)}")
            raise
