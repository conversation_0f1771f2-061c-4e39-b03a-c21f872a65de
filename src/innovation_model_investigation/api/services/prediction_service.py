"""
预测服务组件，负责模型加载和预测执行
"""
import os
import time
import asyncio
from typing import Dict, Any, Optional, List, Tuple, Union
from pathlib import Path
import pandas as pd
import numpy as np
import joblib
from loguru import logger
from functools import lru_cache

from innovation_model_investigation.api.utils.unified_storage import UnifiedModelStorage


class PredictionService:
    """预测服务类，负责模型加载和预测执行"""
    
    def __init__(self, model_storage=None, cache_size: int = 5, use_rustfs: bool = True):
        """
        初始化预测服务
        
        Args:
            model_storage: 模型存储实例，如果为None则创建新实例
            cache_size: 模型缓存大小
            use_rustfs: 是否使用 RustFS 存储
        """
        if model_storage is None:
            self.model_storage = UnifiedModelStorage(base_dir="models", use_rustfs=use_rustfs)
            logger.info(f"使用统一模型存储 (RustFS: {use_rustfs})")
        else:
            self.model_storage = model_storage
            
        self.cache_size = cache_size
        
        # 设置模型缓存装饰器的大小
        self._load_model_files = lru_cache(maxsize=cache_size)(self._load_model_files_uncached)
        
        logger.info(f"初始化预测服务: cache_size={cache_size}")
    
    def _load_model_files_uncached(self, model_id: str, version: Optional[str] = None) -> Dict[str, Any]:
        """
        加载模型文件（未缓存版本）
        
        Args:
            model_id: 模型ID
            version: 版本号，不指定则使用最新版本
            
        Returns:
            模型对象字典
        """
        logger.info(f"加载模型文件: model_id={model_id}, version={version}")
        
        # 获取模型文件路径
        model_files = self.model_storage.load_model(model_id, version)
        
        # 检查必要的文件
        if "model" not in model_files or "woe_encoder" not in model_files:
            raise ValueError(f"模型文件不完整: {model_id}")
        
        # 加载模型文件
        model = joblib.load(model_files["model"])
        woe_encoder = joblib.load(model_files["woe_encoder"])
        
        # 返回模型对象
        return {
            "model": model,
            "woe_encoder": woe_encoder,
            "model_id": model_id,
            "version": version or self.model_storage.index["models"][model_id]["latest"],
            "load_time": time.time(),
        }
    
    async def load_model(self, model_id: str, version: Optional[str] = None) -> Dict[str, Any]:
        """
        加载模型（带缓存）
        
        Args:
            model_id: 模型ID
            version: 版本号，不指定则使用最新版本
            
        Returns:
            模型对象字典
        """
        try:
            return self._load_model_files(model_id, version)
        except Exception as e:
            logger.error(f"加载模型失败: {str(e)}")
            raise
    
    async def predict(self, model_id: str, data: Dict[str, Any], version: Optional[str] = None) -> Dict[str, Any]:
        """
        执行预测
        
        Args:
            model_id: 模型ID
            data: 企业信息数据
            version: 版本号，不指定则使用最新版本
            
        Returns:
            预测结果
        """
        logger.info(f"执行预测: model_id={model_id}")
        
        try:
            # 加载模型
            model_files = await self.load_model(model_id, version)
            model = model_files["model"]
            woe_encoder = model_files["woe_encoder"]
            
            # 准备输入数据
            input_df = pd.DataFrame([data])
            
            # 检查必要的特征
            missing_features = []
            if hasattr(model, "feature_names_in_"):
                required_features = set(model.feature_names_in_)
                available_features = set(input_df.columns)
                missing_features = list(required_features - available_features)
            
            if missing_features:
                raise ValueError(f"输入数据缺少必要的特征: {', '.join(missing_features)}")
            
            # 应用WOE转换
            try:
                woe_transformed_df = woe_encoder.transform(input_df)
            except Exception as e:
                logger.error(f"WOE转换失败: {str(e)}")
                raise ValueError(f"数据转换失败，请检查输入数据格式: {str(e)}")
            
            # 确保模型输入的特征与训练时完全一致
            model_features = model.feature_names_in_
            woe_transformed_df = woe_transformed_df[model_features]
            
            # 预测概率
            probabilities = model.predict_proba(woe_transformed_df)[0]
            prob_unhealthy = probabilities[0]
            prob_healthy = probabilities[1]
            
            # 计算置信度
            confidence = float(max(prob_unhealthy, prob_healthy))
            
            # 确定预测结果
            prediction = "健康" if prob_healthy > prob_unhealthy else "不健康"
            
            # 获取特征重要性
            feature_importance = {}
            if hasattr(model, "coef_"):
                coefficients = model.coef_[0]
                for i, feature in enumerate(model_features):
                    feature_importance[feature] = float(coefficients[i])
            
            # 计算评分卡分数（如果模型支持）
            score = None
            if hasattr(model, "calculate_score"):
                try:
                    score = float(model.calculate_score(woe_transformed_df)[0])
                except Exception as e:
                    logger.error(f"计算评分卡分数失败: {str(e)}")
            
            # 返回预测结果
            return {
                "prediction": prediction,
                "confidence": confidence,
                "explanation": {
                    "feature_importance": feature_importance,
                    "probability_healthy": float(prob_healthy),
                    "probability_unhealthy": float(prob_unhealthy),
                    "score": score,
                },
            }
        except Exception as e:
            logger.exception(f"预测失败: {str(e)}")
            raise
    
    async def predict_batch(self, model_id: str, data: List[Dict[str, Any]], version: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        批量预测
        
        Args:
            model_id: 模型ID
            data: 企业信息数据列表
            version: 版本号，不指定则使用最新版本
            
        Returns:
            预测结果列表
        """
        logger.info(f"执行批量预测: model_id={model_id}, 数据条数={len(data)}")
        
        try:
            # 加载模型
            model_files = await self.load_model(model_id, version)
            model = model_files["model"]
            woe_encoder = model_files["woe_encoder"]
            
            # 准备输入数据
            input_df = pd.DataFrame(data)
            
            # 检查必要的特征
            missing_features = []
            if hasattr(model, "feature_names_in_"):
                required_features = set(model.feature_names_in_)
                available_features = set(input_df.columns)
                missing_features = list(required_features - available_features)
            
            if missing_features:
                raise ValueError(f"输入数据缺少必要的特征: {', '.join(missing_features)}")
            
            # 应用WOE转换
            try:
                woe_transformed_df = woe_encoder.transform(input_df)
            except Exception as e:
                logger.error(f"WOE转换失败: {str(e)}")
                raise ValueError(f"数据转换失败，请检查输入数据格式: {str(e)}")
            
            # 确保模型输入的特征与训练时完全一致
            model_features = model.feature_names_in_
            woe_transformed_df = woe_transformed_df[model_features]
            
            # 预测概率
            probabilities = model.predict_proba(woe_transformed_df)
            
            # 计算评分卡分数（如果模型支持）
            scores = None
            if hasattr(model, "calculate_score"):
                try:
                    scores = model.calculate_score(woe_transformed_df).tolist()
                except Exception as e:
                    logger.error(f"计算评分卡分数失败: {str(e)}")
            
            # 准备结果
            results = []
            for i, probs in enumerate(probabilities):
                prob_unhealthy = probs[0]
                prob_healthy = probs[1]
                
                # 计算置信度
                confidence = max(prob_unhealthy, prob_healthy)
                
                # 确定预测结果
                prediction = "健康" if prob_healthy > prob_unhealthy else "不健康"
                
                result = {
                    "prediction": prediction,
                    "confidence": float(confidence),
                    "probability_healthy": float(prob_healthy),
                    "probability_unhealthy": float(prob_unhealthy),
                }
                
                # 添加评分卡分数（如果有）
                if scores is not None:
                    result["score"] = scores[i]
                
                results.append(result)
            
            return results
        except Exception as e:
            logger.exception(f"批量预测失败: {str(e)}")
            raise
    
    async def calculate_confidence(self, probabilities: np.ndarray) -> float:
        """
        计算预测结果的置信度
        
        Args:
            probabilities: 预测概率数组
            
        Returns:
            置信度
        """
        # 置信度定义为最大概率值
        return float(np.max(probabilities))
    
    def clear_cache(self):
        """清除模型缓存"""
        logger.info("清除模型缓存")
        self._load_model_files.cache_clear()
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存信息
        
        Returns:
            缓存信息
        """
        cache_info = self._load_model_files.cache_info()
        return {
            "hits": cache_info.hits,
            "misses": cache_info.misses,
            "maxsize": cache_info.maxsize,
            "currsize": cache_info.currsize,
        }