"""
核心训练逻辑，基于 pipeline.py 的验证流程
"""

import json
import time
import toml
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
from loguru import logger

# 导入训练相关模块
try:
    from innovation_model_investigation.pipeline import main as run_pipeline

    TRAINING_MODULES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"无法导入训练模块: {str(e)}")
    TRAINING_MODULES_AVAILABLE = False


class CoreTrainingEngine:
    """核心训练引擎，基于 pipeline.py 的验证流程"""

    def __init__(self):
        """初始化核心训练引擎"""

    def check_version_exists(
        self, version: str, base_dir: Path = Path("outputs")
    ) -> bool:
        """检查版本是否已存在"""
        version_dir = base_dir / version
        return version_dir.exists()

    async def execute_training_with_pipeline(
        self,
        version: str,
        sample_data_path: Optional[str] = None,
        force_overwrite: bool = False,
    ) -> Tuple[str, Dict[str, Any]]:
        """
        使用现有pipeline.py执行训练并保存到RustFS

        Args:
            output_dir: 输出目录
            version: 可选的版本号，如果不提供则从 pyproject.toml 读取
            sample_data_path: 样本数据文件路径
            force_overwrite: 是否强制覆盖已存在的版本

        Returns:
            版本号和训练结果
        """
        logger.info("🚀 开始执行pipeline训练流程")
        start_time = time.time()

        if not TRAINING_MODULES_AVAILABLE:
            raise RuntimeError("训练模块不可用")

        # 版本管理逻辑
        # API 执行：使用传入版本
        logger.info(f"📋 API执行，使用指定版本: {version}")

        # 检查版本是否已存在
        if self.check_version_exists(version):
            if not force_overwrite:
                raise ValueError(
                    f"版本 {version} 已存在！请使用不同的版本号或设置 force_overwrite=True 强制覆盖"
                )
            else:
                logger.warning(f"⚠️ 强制覆盖已存在的版本: {version}")

        try:
            # 执行现有的pipeline流程，传入版本号和样本数据路径
            logger.info(
                f"📊 执行pipeline.main(version='{version}', sample_data_path='{sample_data_path}')..."
            )

            # 根据sample_data_path是否为None来决定调用参数
            if sample_data_path is not None:
                results = run_pipeline(
                    version=version, sample_data_path=sample_data_path
                )
            else:
                results = run_pipeline(version=version)

            # 从结果中提取版本信息
            pipeline_output_dir = results.get("output_dir")
            if pipeline_output_dir is None:
                # 如果 pipeline 没有返回 output_dir，构建默认路径
                pipeline_output_dir = f"outputs/{version}"

            actual_version = Path(pipeline_output_dir).name
            if actual_version != version:
                logger.warning(f"⚠️ 版本不匹配: 期望={version}, 实际={actual_version}")
                version = actual_version

            # 提取评估结果
            evaluation_results = results.get("evaluation_results", {})
            metrics = evaluation_results.get("classification_metrics", {})

            # 构建文件路径
            pipeline_dir = Path(pipeline_output_dir)
            files = {}

            # 查找所有重要文件（包括 pkl, csv, json 等）
            important_subdirs = [
                "models",
                "feature",
                "evaluation",
                "binning",
                "baseline",
            ]

            for subdir in important_subdirs:
                subdir_path = pipeline_dir / subdir
                if subdir_path.exists():
                    # 收集所有文件类型：pkl, csv, json
                    for file_pattern in ["*.pkl", "*.csv", "*.json"]:
                        for file_path in subdir_path.glob(file_pattern):
                            # 使用 subdir_filename 作为键，避免重名冲突
                            file_type = f"{subdir}_{file_path.stem}"
                            files[file_type] = file_path
                            logger.debug(f"收集文件: {file_type} -> {file_path}")

            # 确保evaluation_results.json文件存在并被包含
            evaluation_results_path = pipeline_dir / "evaluation_results.json"
            if not evaluation_results_path.exists() and evaluation_results:
                # 如果evaluation_results.json不存在但有评估结果，创建它
                logger.info("📊 创建evaluation_results.json文件...")
                with open(evaluation_results_path, "w", encoding="utf-8") as f:
                    json.dump(evaluation_results, f, ensure_ascii=False, indent=2)
                logger.info(
                    f"✅ 已创建evaluation_results.json: {evaluation_results_path}"
                )

            if evaluation_results_path.exists():
                files["evaluation_results"] = evaluation_results_path
                logger.info("📊 已包含evaluation_results.json文件")

            logger.info(f"📁 收集到 {len(files)} 个文件，包含以下类型:")
            for file_type, file_path in files.items():
                logger.info(f"  - {file_type}: {file_path.name}")

            # 构建元数据
            metadata = {
                "version": version,
                "created_at": datetime.now().isoformat(),
                "training_time": time.time() - start_time,
                "metrics": metrics,
                "pipeline_output_dir": str(pipeline_dir),
                "evaluation_results": evaluation_results,
                "sample_data_path": sample_data_path,
            }

            # 构建结果
            result = {
                "version": version,
                "metrics": metrics,
                "training_time": time.time() - start_time,
                "output_dir": str(pipeline_dir),
                "files": files,
                "evaluation_results": evaluation_results,
                "metadata": metadata,
            }

            logger.info(f"✅ Pipeline训练完成: 版本={version}, 找到{len(files)}个文件")
            return version, result

        except Exception as e:
            logger.exception(f"❌ Pipeline训练失败: {str(e)}")
            raise
