"""
使用 rustfs 的模型存储实现，提供更高性能的文件操作
"""

import os
import json
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional
import tempfile

from loguru import logger
import boto3
from botocore.client import Config

# 全局常量定义
MODEL_BASE_DIR = "innovation_health_model"  # 模型基础目录名称
METADATA_FILENAME = "metadata.json"  # 元数据文件名


class RustFSModelStorage:
    """使用 rustfs 的模型存储类，负责管理模型文件的存储和访问"""

    def __init__(
        self,
        base_dir: str = "models",
        server_url: str = "http://172.18.102.250:6200",
        access_key: str = "kzz",
        secret_key: str = "kzz_2025",
    ):
        """
        初始化模型存储

        Args:
            base_dir: 模型存储基础目录
            server_url: RustFS 服务器地址，如果为 None 则从环境变量 RUSTFS_SERVER_URL 获取
            access_key: RustFS 访问密钥，如果为 None 则从环境变量 RUSTFS_ACCESS_KEY 获取
            secret_key: RustFS 密钥，如果为 None 则从环境变量 RUSTFS_SECRET_KEY 获取
        """
        # 从环境变量获取配置
        self.server_url = os.environ.get("RUSTFS_SERVER_URL") or server_url
        self.access_key = os.environ.get("RUSTFS_ACCESS_KEY") or access_key
        self.secret_key = os.environ.get("RUSTFS_SECRET_KEY") or secret_key
        self.bucket_name = (
            os.environ.get("RUSTFS_BUCKET_NAME") or "kzz-model-training-platform"
        )

        # 记录配置信息
        logger.info(
            f"RustFS 配置: server_url={self.server_url}, bucket={self.bucket_name}"
        )

        # 创建 boto3 S3 客户端
        self.s3_client = boto3.client(
            "s3",
            endpoint_url=self.server_url,
            aws_access_key_id=self.access_key,
            aws_secret_access_key=self.secret_key,
            config=Config(signature_version="s3v4"),
            region_name="us-east-1",  # 这个值可能需要根据您的 rustfs 配置调整
        )

        # 创建 boto3 S3 资源
        self.s3_resource = boto3.resource(
            "s3",
            endpoint_url=self.server_url,
            aws_access_key_id=self.access_key,
            aws_secret_access_key=self.secret_key,
            config=Config(signature_version="s3v4"),
            region_name="us-east-1",  # 这个值可能需要根据您的 rustfs 配置调整
        )

        # 确保 bucket 存在
        try:
            # 尝试列出 bucket 中的对象，而不是使用 head_bucket
            # 这可能会绕过一些权限问题
            self.s3_client.list_objects_v2(Bucket=self.bucket_name, MaxKeys=1)
            logger.info(f"成功访问 Bucket: {self.bucket_name}")
        except Exception as e:
            logger.warning(f"访问 bucket 失败: {str(e)}，将尝试直接操作")

            # 打印更多调试信息
            logger.info(
                f"使用的配置: endpoint_url={self.server_url}, access_key={self.access_key[:4]}..., bucket={self.bucket_name}"
            )

            # 尝试列出所有可用的 buckets
            try:
                response = self.s3_client.list_buckets()
                buckets = [bucket["Name"] for bucket in response["Buckets"]]
                logger.info(f"可用的 buckets: {buckets}")
            except Exception as list_e:
                logger.warning(f"列出 buckets 失败: {str(list_e)}")

        self.base_dir = Path(base_dir)
        self.model_dir = self.base_dir / MODEL_BASE_DIR
        # 创建基础目录
        os.makedirs(str(self.model_dir), exist_ok=True)
        self.index_file = self.model_dir / "index.json"
        self._load_index()

    def _load_index(self):
        """加载模型索引文件"""
        if os.path.exists(str(self.index_file)):
            with open(str(self.index_file), "r", encoding="utf-8") as f:
                self.index = json.load(f)
        else:
            self.index = {"versions": [], "latest": None}
            self._save_index()

    def _save_index(self):
        """保存模型索引文件"""
        with open(str(self.index_file), "w", encoding="utf-8") as f:
            json.dump(self.index, f, ensure_ascii=False, indent=2)

    def save_model(
        self, version: str, files: Dict[str, Path], metadata: Dict[str, Any]
    ) -> str:
        """
        保存模型文件

        Args:
            version: 版本号
            files: 文件路径字典，键为文件类型，值为文件路径
            metadata: 模型元数据

        Returns:
            模型目录路径
        """
        # 检查是否包含评估结果文件
        has_evaluation_results = "evaluation_results" in files
        if not has_evaluation_results:
            logger.warning(f"模型 {version} 没有包含评估结果文件")
            # 检查元数据中是否包含评估结果
            if "evaluation_results" in metadata:
                logger.info(f"从元数据中提取评估结果: {version}")
                # 创建临时评估结果文件
                import tempfile

                with tempfile.NamedTemporaryFile(
                    mode="w", suffix=".json", delete=False
                ) as temp_file:
                    json.dump(
                        metadata["evaluation_results"],
                        temp_file,
                        ensure_ascii=False,
                        indent=2,
                    )
                    temp_path = temp_file.name
                files["evaluation_results"] = Path(temp_path)
        # 创建模型版本目录
        version_dir = self.model_dir / version
        os.makedirs(str(version_dir), exist_ok=True)

        # 复制文件
        for file_type, file_path in files.items():
            if not os.path.exists(str(file_path)):
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 保留原始文件扩展名
            original_ext = file_path.suffix
            # 保留原始文件扩展名
            target_path = version_dir / f"{file_type}{original_ext}"
            s3_key = f"{MODEL_BASE_DIR}/{version}/{file_type}{original_ext}"

            # 复制文件
            shutil.copy2(str(file_path), str(target_path))
            logger.info(f"复制文件: {file_path} -> {target_path}")

            # 上传到 RustFS
            try:
                self.s3_client.upload_file(
                    Filename=str(file_path), Bucket=self.bucket_name, Key=s3_key
                )
                logger.info(f"上传文件到 RustFS: {file_path} -> {s3_key}")
            except Exception as e:
                logger.warning(f"上传文件到 RustFS 失败: {str(e)}")

        # 保存元数据
        metadata_path = version_dir / METADATA_FILENAME
        metadata_content = json.dumps(metadata, ensure_ascii=False, indent=2)
        with open(str(metadata_path), "w", encoding="utf-8") as f:
            f.write(metadata_content)

        # 上传元数据到 RustFS
        s3_metadata_key = f"{MODEL_BASE_DIR}/{version}/{METADATA_FILENAME}"
        try:
            self.s3_client.upload_file(
                Filename=str(metadata_path),
                Bucket=self.bucket_name,
                Key=s3_metadata_key,
            )
        except Exception as e:
            logger.warning(f"上传元数据到 RustFS 失败: {str(e)}")

        # 更新索引
        if version not in self.index["versions"]:
            self.index["versions"].append(version)
        self.index["latest"] = version
        self._save_index()

        # 创建latest符号链接
        latest_link = self.model_dir / "latest"
        if os.path.exists(str(latest_link)):
            os.remove(str(latest_link))
        os.symlink(version, str(latest_link), target_is_directory=True)

        return str(version_dir)

    def load_model(self, version: str) -> Dict[str, Path]:
        """
        加载模型文件

        Args:
            version: 版本号，不指定则使用最新版本

        Returns:
            文件路径字典
        """
        if not self.index["versions"]:
            raise ValueError(f"没有可用的模型版本")

        if version is None:
            if self.index["latest"] is None:
                raise ValueError(f"没有设置最新版本")
            version = self.index["latest"]
        elif version not in self.index["versions"]:
            raise ValueError(f"模型版本不存在: {version}")

        version_dir = self.model_dir / version
        if not os.path.exists(str(version_dir)):
            os.makedirs(str(version_dir), exist_ok=True)

        # 从 RustFS 下载文件
        s3_prefix = f"{MODEL_BASE_DIR}/{version}/"
        files = {}

        # 列出 RustFS 中的文件
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name, Prefix=s3_prefix
            )

            if "Contents" in response:
                for item in response["Contents"]:
                    s3_key = item["Key"]
                    # 支持.pkl和.json文件（特别是evaluation_results.json）
                    if s3_key.endswith((".pkl", ".json")):
                        file_name = os.path.basename(s3_key)
                        file_type = os.path.splitext(file_name)[0]
                        local_path = version_dir / file_name

                        # 下载文件
                        if not os.path.exists(str(local_path)):
                            self.s3_client.download_file(
                                Bucket=self.bucket_name,
                                Key=s3_key,
                                Filename=str(local_path),
                            )

                        files[file_type] = local_path
        except Exception as e:
            logger.warning(f"从 RustFS 加载文件失败: {str(e)}，尝试从本地加载")

            # 如果从 RustFS 加载失败，尝试从本地加载
            for file_path in version_dir.glob("*.pkl"):
                file_type = file_path.stem
                files[file_type] = file_path

        if not files:
            raise FileNotFoundError(f"模型文件不存在: {version}")

        return files

    def get_model_info(self, version: str) -> Dict[str, Any]:
        """
        获取模型信息

        Args:
            version: 版本号，不指定则使用最新版本

        Returns:
            模型元数据
        """
        if not self.index["versions"]:
            raise ValueError(f"没有可用的模型版本")

        if version not in self.index["versions"]:
            raise ValueError(f"模型版本不存在: {version}")

        metadata_path = self.model_dir / version / METADATA_FILENAME
        s3_metadata_key = f"{MODEL_BASE_DIR}/{version}/{METADATA_FILENAME}"

        # 尝试从 RustFS 获取元数据
        try:
            if not os.path.exists(str(metadata_path)):
                os.makedirs(os.path.dirname(str(metadata_path)), exist_ok=True)
                self.s3_client.download_file(
                    Bucket=self.bucket_name,
                    Key=s3_metadata_key,
                    Filename=str(metadata_path),
                )
        except Exception as e:
            logger.warning(f"从 RustFS 获取元数据失败: {str(e)}")

        # 从本地读取元数据
        if not os.path.exists(str(metadata_path)):
            raise FileNotFoundError(f"模型元数据文件不存在: {metadata_path}")

        with open(str(metadata_path), "r", encoding="utf-8") as f:
            metadata = json.load(f)

        return metadata

    def list_models(self) -> List[Dict[str, Any]]:
        """
        列出所有模型版本

        Returns:
            模型版本列表
        """
        models = []
        for version in self.index["versions"]:
            try:
                metadata = self.get_model_info(version)
                models.append(
                    {
                        "version": version,
                        "source": "rustfs",
                        "is_latest": version == self.index["latest"],
                        "created_at": metadata.get("created_at"),
                        "metrics": metadata.get("metrics", {}),
                    }
                )
            except Exception as e:
                logger.error(f"获取模型信息失败: {version}, 错误: {str(e)}")

        return models

    def delete_model(self, version: Optional[str] = None) -> bool:
        """
        删除模型

        Args:
            version: 版本号，不指定则删除所有版本

        Returns:
            是否删除成功
        """
        if not self.index["versions"]:
            raise ValueError(f"没有可用的模型版本")

        if version is None:
            # 删除所有版本
            if os.path.exists(str(self.model_dir)):
                shutil.rmtree(str(self.model_dir))
                os.makedirs(str(self.model_dir), exist_ok=True)

            # 删除 RustFS 中的文件
            try:
                # 列出所有对象
                response = self.s3_client.list_objects_v2(
                    Bucket=self.bucket_name, Prefix=f"{MODEL_BASE_DIR}/"
                )

                if "Contents" in response:
                    # 删除所有对象
                    objects_to_delete = [
                        {"Key": item["Key"]} for item in response["Contents"]
                    ]
                    self.s3_client.delete_objects(
                        Bucket=self.bucket_name, Delete={"Objects": objects_to_delete}
                    )
            except Exception as e:
                logger.warning(f"删除 RustFS 文件失败: {str(e)}")

            # 重置索引
            self.index = {"versions": [], "latest": None}
            self._save_index()

            return True
        else:
            if version not in self.index["versions"]:
                raise ValueError(f"模型版本不存在: {version}")

            # 删除指定版本
            version_dir = self.model_dir / version
            if os.path.exists(str(version_dir)):
                shutil.rmtree(str(version_dir))

            # 删除 RustFS 中的文件
            try:
                # 列出所有对象
                response = self.s3_client.list_objects_v2(
                    Bucket=self.bucket_name, Prefix=f"{MODEL_BASE_DIR}/{version}/"
                )

                if "Contents" in response:
                    # 删除所有对象
                    objects_to_delete = [
                        {"Key": item["Key"]} for item in response["Contents"]
                    ]
                    self.s3_client.delete_objects(
                        Bucket=self.bucket_name, Delete={"Objects": objects_to_delete}
                    )
            except Exception as e:
                logger.warning(f"删除 RustFS 文件失败: {str(e)}")

            # 更新索引
            self.index["versions"].remove(version)

            # 如果删除的是最新版本，更新latest
            if self.index["latest"] == version:
                if self.index["versions"]:
                    # 使用最新的版本作为latest
                    self.index["latest"] = sorted(self.index["versions"])[-1]

                    # 更新latest符号链接
                    latest_link = self.model_dir / "latest"
                    if os.path.exists(str(latest_link)):
                        os.remove(str(latest_link))
                    os.symlink(
                        self.index["latest"], str(latest_link), target_is_directory=True
                    )
                else:
                    # 如果没有版本了，设置latest为None
                    self.index["latest"] = None

            self._save_index()
            return True

    def list_model_files(self, version: str) -> List[str]:
        """
        列出模型文件

        Args:
            version: 版本号，不指定则使用最新版本

        Returns:
            文件名列表，包括evaluation_results.json
        """
        if not self.index["versions"]:
            raise ValueError("没有可用的模型版本")

        if version not in self.index["versions"]:
            raise ValueError(f"模型版本不存在: {version}")

        version_dir = self.model_dir / version
        if not os.path.exists(str(version_dir)):
            raise FileNotFoundError(f"模型目录不存在: {version_dir}")

        # 从本地获取文件列表
        files = []
        for file_path in Path(version_dir).glob("*.*"):
            files.append(file_path.name)

        # 从RustFS获取文件列表
        try:
            s3_prefix = f"{MODEL_BASE_DIR}/{version}/"
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name, Prefix=s3_prefix
            )

            if "Contents" in response:
                for item in response["Contents"]:
                    s3_key = item["Key"]
                    file_name = os.path.basename(s3_key)
                    if file_name not in files:
                        files.append(file_name)
                        logger.info(f"从S3中添加文件: {file_name}")

            # 打印S3中的所有文件
            logger.info(
                f"S3中的文件: {[item['Key'] for item in response.get('Contents', [])]}"
            )
            logger.info(f"当前文件列表: {files}")
        except Exception as e:
            logger.warning(f"从RustFS获取文件列表失败: {str(e)}")

        # 确保evaluation_results.json或evaluation_results.pkl在列表中
        has_evaluation_results = False
        for file in files:
            if (
                file.startswith("evaluation_results.")
                or file == "evaluation_results.json"
                or file == "evaluation_results.pkl"
                or file == "evaluation_evaluation_results.json"
                or file == "evaluation_evaluation_results.pkl"
            ):
                has_evaluation_results = True
                break

        if not has_evaluation_results:
            # 检查元数据中是否包含评估结果
            try:
                metadata = self.get_model_info(version)
                if "evaluation_results" in metadata:
                    files.append("evaluation_results.json")
                    logger.info(f"从元数据中检测到评估结果: {version}")
                else:
                    # 检查是否有evaluation_results文件在S3中
                    try:
                        s3_key = f"{MODEL_BASE_DIR}/{version}/evaluation_results.json"
                        self.s3_client.head_object(Bucket=self.bucket_name, Key=s3_key)
                        files.append("evaluation_results.json")
                        logger.info(f"从S3中检测到评估结果文件: {version}")
                    except Exception as s3_err:
                        logger.debug(f"S3中未找到评估结果文件JSON: {str(s3_err)}")
                        # 尝试查找.pkl格式的评估结果
                        try:
                            s3_key = (
                                f"{MODEL_BASE_DIR}/{version}/evaluation_results.pkl"
                            )
                            self.s3_client.head_object(
                                Bucket=self.bucket_name, Key=s3_key
                            )
                            files.append("evaluation_results.pkl")
                            logger.info(f"从S3中检测到评估结果文件PKL: {version}")
                        except Exception as pkl_err:
                            logger.debug(f"S3中未找到评估结果文件PKL: {str(pkl_err)}")
            except Exception as e:
                logger.warning(f"检查元数据中的评估结果失败: {str(e)}")

        return files

    def get_model_size(self, version: str) -> int:
        """
        获取模型大小

        Args:
            version: 版本号，不指定则使用最新版本

        Returns:
            模型大小（字节）
        """
        if not self.index["versions"]:
            raise ValueError("没有可用的模型版本")

        if version not in self.index["versions"]:
            raise ValueError(f"模型版本不存在: {version}")

        version_dir = self.model_dir / version
        if not os.path.exists(str(version_dir)):
            raise FileNotFoundError(f"模型目录不存在: {version_dir}")

        # 计算目录大小
        total_size = 0
        for root, dirs, files in os.walk(str(version_dir)):
            for file in files:
                file_path = os.path.join(root, file)
                total_size += os.path.getsize(file_path)

        return total_size

    def export_model(self, target_path: str, version: str) -> str:
        """
        导出模型到指定路径

        Args:
            target_path: 目标路径
            version: 版本号，不指定则使用最新版本

        Returns:
            导出的文件路径
        """
        if not self.index["versions"]:
            raise ValueError(f"没有可用的模型版本")

        if version not in self.index["versions"]:
            raise ValueError(f"模型版本不存在: {version}")

        version_dir = self.model_dir / version
        if not os.path.exists(str(version_dir)):
            raise FileNotFoundError(f"模型目录不存在: {version_dir}")

        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        temp_archive = os.path.join(temp_dir, f"{MODEL_BASE_DIR}_{version}.tar.gz")

        # 创建归档
        import tarfile

        with tarfile.open(temp_archive, "w:gz") as tar:
            tar.add(str(version_dir), arcname=version)

        # 复制到目标路径
        target_file = os.path.join(target_path, f"{MODEL_BASE_DIR}_{version}.tar.gz")
        shutil.copy2(temp_archive, target_file)

        # 清理临时文件
        shutil.rmtree(temp_dir)

        return target_file


class RustFSModelStorageAdapter:
    """
    RustFSModelStorage 适配器，使其与 ModelStorage 接口兼容
    """

    def __init__(
        self,
        base_dir: str = "models",
        server_url: str = "http://172.18.102.250:6200",
        access_key: str = "kzz",
        secret_key: str = "kzz_2025",
    ):
        """
        初始化适配器

        Args:
            base_dir: 模型存储基础目录
            server_url: RustFS 服务器地址，如果为 None 则从环境变量 RUSTFS_SERVER_URL 获取
            access_key: RustFS 访问密钥，如果为 None 则从环境变量 RUSTFS_ACCESS_KEY 获取
            secret_key: RustFS 密钥，如果为 None 则从环境变量 RUSTFS_SECRET_KEY 获取
        """
        self.storage = RustFSModelStorage(base_dir, server_url, access_key, secret_key)
        self.base_dir = Path(base_dir)
        self.index = self.storage.index

    def save_model(
        self,
        version: str,
        files: Dict[str, Path],
        metadata: Dict[str, Any],
    ) -> str:
        """
        保存模型文件

        Args:
            version: 版本号
            files: 文件路径字典，键为文件类型，值为文件路径
            metadata: 模型元数据

        Returns:
            模型目录路径
        """
        return self.storage.save_model(version, files, metadata)

    def load_model(self, version: str) -> Dict[str, Path]:
        """
        加载模型文件

        Args:
            version: 版本号，不指定则使用最新版本

        Returns:
            文件路径字典
        """
        return self.storage.load_model(version)

    def get_model_info(self, version: str) -> Dict[str, Any]:
        """
        获取模型信息

        Args:
            version: 版本号，不指定则使用最新版本

        Returns:
            模型元数据
        """
        return self.storage.get_model_info(version)

    def list_models(self) -> List[Dict[str, Any]]:
        """
        列出所有模型版本

        Returns:
            模型版本列表
        """
        return self.storage.list_models()

    def delete_model(self, version: str) -> bool:
        """
        删除模型

        Args:
            version: 版本号，不指定则删除所有版本

        Returns:
            是否删除成功
        """
        return self.storage.delete_model(version)

    def get_model_size(self, version: str) -> int:
        """
        获取模型大小

        Args:
            version: 版本号，不指定则使用最新版本

        Returns:
            模型大小（字节）
        """
        return self.storage.get_model_size(version)

    def export_model(self, target_path: str, version: str) -> str:
        """
        导出模型到指定路径

        Args:
            target_path: 目标路径
            version: 版本号，不指定则使用最新版本

        Returns:
            导出的文件路径
        """
        return self.storage.export_model(target_path, version)
