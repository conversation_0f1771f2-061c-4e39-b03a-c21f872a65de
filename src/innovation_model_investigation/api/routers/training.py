"""
训练相关API路由
"""

import asyncio
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, BackgroundTasks
from loguru import logger

from innovation_model_investigation.api.models.training import (
    TrainingRequest,
    TrainingResponse,
    ErrorResponse,
)
from innovation_model_investigation.api.services.training_service import TrainingService
from innovation_model_investigation.api.utils.unified_storage import make_serializable

router = APIRouter(prefix="/innovation_health_model/api/v1", tags=["训练"])

# 全局训练服务实例
training_service = TrainingService()

# 活跃任务计数
active_tasks = 0


@router.post("/train", response_model=TrainingResponse)
async def train_model(request: TrainingRequest, background_tasks: BackgroundTasks):
    """
    训练模型API
    """
    global active_tasks

    try:
        active_tasks += 1
        logger.info(
            f"🚀 收到训练请求: {request.traningSampleDataPath}, 版本: {request.modelVersion}"
        )

        # 执行训练
        model_id, result = await training_service.train_model(
            traningSampleDataPath=request.traningSampleDataPath,
            modelVersion=request.modelVersion,
        )

        return TrainingResponse(
            model_id=model_id, status="completed", message="训练完成", **result
        )

    except Exception as e:
        logger.exception(f"❌ 训练失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        active_tasks -= 1


@router.get("/models")
async def list_models():
    """
    获取模型列表
    """
    try:
        models = training_service.list_models()
        return make_serializable({"models": models}, handle_special_floats=True)
    except Exception as e:
        logger.exception(f"❌ 获取模型列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/models/{model_id}")
async def get_model_info(model_id: str):
    """
    获取模型信息
    """
    try:
        info = training_service.get_model_info(model_id)
        return info
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="模型不存在")
    except Exception as e:
        logger.exception(f"❌ 获取模型信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
