"""
训练API的数据模型
"""

from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from datetime import datetime


class TrainingRequest(BaseModel):
    """训练请求模型"""

    traningSampleDataPath: str = Field(..., description="训练样本数据路径")
    modelVersion: str = Field(..., description="模型版本")
    callback_url: Optional[str] = Field(None, description="回调URL，用于通知训练完成")


class TrainingResponse(BaseModel):
    """训练响应模型"""

    model_id: str = Field(..., description="模型ID")
    version: str = Field(..., description="版本号")
    metrics: Dict[str, float] = Field(..., description="性能指标")
    training_time: float = Field(..., description="训练时间（秒）")
    status: str = Field(default="success", description="状态")
    error: Optional[str] = Field(None, description="错误信息")
    message: Optional[str] = Field(None, description="响应消息")


class PredictionRequest(BaseModel):
    """预测请求模型"""

    model_id: str = Field(..., description="模型ID")
    data: Dict[str, Any] = Field(..., description="企业信息数据")
    version: Optional[str] = Field(None, description="模型版本，不指定则使用最新版本")


class PredictionResponse(BaseModel):
    """预测响应模型"""

    prediction: Any = Field(..., description="预测结果")
    confidence: float = Field(..., description="置信度")
    explanation: Optional[Dict[str, Any]] = Field(None, description="预测解释")
    status: str = Field(default="success", description="状态")
    error: Optional[str] = Field(None, description="错误信息")


class HealthResponse(BaseModel):
    """健康检查响应模型"""

    status: str = Field(..., description="服务状态")
    version: str = Field(..., description="API版本")
    uptime: float = Field(..., description="运行时间（秒）")
    resources: Dict[str, float] = Field(..., description="资源使用情况")
    active_tasks: int = Field(..., description="活动任务数")


class ModelInfo(BaseModel):
    """模型信息模型"""

    model_id: str = Field(..., description="模型ID")
    version: str = Field(..., description="版本号")
    created_at: datetime = Field(..., description="创建时间")
    parameters: Dict[str, Any] = Field(..., description="训练参数")
    metrics: Dict[str, float] = Field(..., description="性能指标")
    file_path: str = Field(..., description="模型文件路径")
    size: int = Field(..., description="模型文件大小（字节）")
    training_set_id: str = Field(..., description="训练集ID")


class ErrorResponse(BaseModel):
    """错误响应模型"""

    status: str = Field(default="error", description="状态")
    error_code: str = Field(..., description="错误代码")
    message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间")
    request_id: str = Field(..., description="请求ID")
