
---
# 企业风险评估模型权重优化项目 Cursor Rules

## 项目概述
这是一个基于WOE分箱和逻辑回归的评分卡优化系统，目标是将人工拍脑袋的权重体系转换为基于数据驱动的科学权重体系。

## 技术栈
- Python 3.11+
- 机器学习：scikit-learn, scipy
- 评分卡专用：scorecardpy, optbinning
- 数据处理：pandas, numpy
- 可视化：matplotlib, seaborn, plotly
- 模型解释：shap
- 开发工具：jupyter, pytest

## 代码规范

### 通用原则
- 优先使用中文注释和文档字符串
- 遵循PEP 8代码风格
- 使用有意义的变量名（如 `user_count` 而非 `uc`）
- 保持函数简洁，单个文件控制在500行以内
- 优先考虑可读性和可维护性

### 文件结构
- 使用 src/ 布局：`src/innovation_model_investigation/`
- 模块化设计，按功能分离代码
- 所有Python包必须包含 `__init__.py`

### 机器学习特定规范
- 数据预处理步骤要清晰记录
- 模型训练过程要包含评估指标（AUC, KS值等）
- 分箱策略要有业务解释性
- WOE编码过程要可追溯
- 权重学习结果要可解释

### 数据处理
- 使用pandas进行数据操作
- 异常值处理要明确记录
- 缺失值处理策略要说明
- 数据验证使用pydantic

### 可视化
- 图表要有中文标题和标签
- 使用seaborn/matplotlib做统计图
- 使用plotly做交互式图表
- 保存图表时使用高分辨率

## 开发环境规范

### 虚拟环境管理
- **重要**：执行任何pip安装命令前，必须先激活虚拟环境：`source .venv/bin/activate` ,然后使用 uv pip install 来安装包
- 使用项目根目录的 `.venv` 虚拟环境
- 依赖管理统一使用 `pyproject.toml`
- 不要创建 `requirements.txt`

### 测试规范
- 使用pytest进行单元测试
- 测试文件命名：`test_*.py`
- 关键算法必须有测试覆盖
- 数据处理流程要有集成测试

### Git规范
- 提交信息使用中文
- 小步频繁提交
- 分支命名：feature/功能名, fix/修复名
- 敏感数据不要提交到仓库

## 业务领域特定规则

### 企业风险评估
- 指标定义要有业务含义说明
- 分箱逻辑要符合风险递增原则
- 权重调整要有统计显著性验证
- 模型效果要与业务目标对齐

### 评分卡建模
- IV值计算要准确
- WOE值要单调性检查
- PSI监控要实现
- 模型稳定性要评估

### 数据质量
- 样本分布要检查
- 标签质量要验证
- 特征相关性要分析
- 过拟合要预防

## 代码示例模板

### 数据预处理
```python
def preprocess_enterprise_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    企业数据预处理
    
    Args:
        df: 原始企业数据
        
    Returns:
        清洗后的企业数据
    """
    # 缺失值处理
    # 异常值检测
    # 数据类型转换
    return df
```

### 分箱函数
```python
def optimal_binning(feature: pd.Series, target: pd.Series) -> dict:
    """
    最优分箱
    
    Args:
        feature: 特征变量
        target: 目标变量（0/1）
        
    Returns:
        分箱结果字典
    """
    # 使用optbinning库
    # 返回切点和WOE值
    pass
```

### 模型训练
```python
def train_scorecard_model(X: pd.DataFrame, y: pd.Series) -> dict:
    """
    训练评分卡模型
    
    Args:
        X: 特征矩阵（WOE编码后）
        y: 目标变量
        
    Returns:
        训练结果字典，包含模型和评估指标
    """
    # 逻辑回归训练
    # 模型评估（AUC, KS, etc.）
    # 权重提取
    pass
```

## 文档规范
- 所有公共函数必须有docstring
- README要包含项目背景和使用方法
- 重要算法要有理论说明
- 模型结果要有业务解释
## 部署和监控
- 模型版本要管理
- 预测结果要记录
- 模型性能要监控
- 数据漂移要检测

## 注意事项
- 企业数据涉及隐私，注意数据安全
- 模型解释性比准确性更重要
- 业务规则和统计模型要平衡
- 监管要求要考虑

## AI助手指导原则
1. 优先生成可直接运行的完整代码
2. 包含必要的异常处理和数据验证
3. 提供详细的中文注释
4. 考虑业务场景的实际需求
5. 确保代码的可扩展性和可维护性
6. **执行任何安装命令前必须提醒激活虚拟环境** 
# MCP Interactive Feedback Rules

1. During any process, task, or conversation, whether asking, responding, or completing stage tasks, must call MCP mcp-feedback-enhanced.
2. When receiving user feedback, if feedback content is not empty, must call MCP mcp-feedback-enhanced again and adjust behavior based on feedback.
3. Only when user explicitly indicates "end" or "no more interaction needed" can you stop calling MCP mcp-feedback-enhanced, then the process is complete.
4. Unless receiving end command, all steps must repeatedly call MCP mcp-feedback-enhanced.
5. Before completing the task, use the MCP mcp-feedback-enhanced to ask the user for feedback.