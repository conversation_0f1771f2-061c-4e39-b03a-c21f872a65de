"""
PredictionService单元测试
"""
import os
import sys
import unittest
from unittest.mock import MagicMock, patch
import pandas as pd
import numpy as np
import joblib
from pathlib import Path
import tempfile
import shutil

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from innovation_model_investigation.api.services.prediction_service import PredictionService
from innovation_model_investigation.api.utils.unified_storage import UnifiedModelStorage


class TestPredictionService(unittest.TestCase):
    """PredictionService单元测试类"""

    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.models_dir = os.path.join(self.temp_dir, "models")
        os.makedirs(self.models_dir, exist_ok=True)

        # 创建模拟的模型存储
        self.model_storage = UnifiedModelStorage(base_dir=self.models_dir, use_rustfs=False)
        
        # 创建预测服务
        self.prediction_service = PredictionService(model_storage=self.model_storage, cache_size=2)
        
        # 创建模拟的模型和编码器
        self.mock_model = MagicMock()
        self.mock_woe_encoder = MagicMock()
        
        # 设置模型的预测行为
        self.mock_model.predict_proba.return_value = np.array([[0.3, 0.7]])
        self.mock_model.feature_names_in_ = ["feature1", "feature2"]
        
        # 设置WOE编码器的转换行为
        self.mock_woe_encoder.transform.return_value = pd.DataFrame({
            "feature1": [0.5],
            "feature2": [0.8]
        })
        
        # 模拟模型存储的load_model方法
        self.model_storage.load_model = MagicMock(return_value={
            "model": Path(os.path.join(self.models_dir, "model.pkl")),
            "woe_encoder": Path(os.path.join(self.models_dir, "woe_encoder.pkl"))
        })
        
        # 保存模拟的模型和编码器
        joblib.dump(self.mock_model, os.path.join(self.models_dir, "model.pkl"))
        joblib.dump(self.mock_woe_encoder, os.path.join(self.models_dir, "woe_encoder.pkl"))

    def tearDown(self):
        """测试后清理"""
        # 删除临时目录
        shutil.rmtree(self.temp_dir)

    @patch('joblib.load')
    async def test_load_model(self, mock_joblib_load):
        """测试加载模型"""
        # 设置joblib.load的返回值
        mock_joblib_load.side_effect = [self.mock_model, self.mock_woe_encoder]
        
        # 调用load_model方法
        model_files = await self.prediction_service.load_model("test_model")
        
        # 验证结果
        self.assertEqual(model_files["model"], self.mock_model)
        self.assertEqual(model_files["woe_encoder"], self.mock_woe_encoder)
        
        # 验证模型存储的load_model方法被调用
        self.model_storage.load_model.assert_called_once_with("test_model", None)
        
        # 验证joblib.load被调用了两次
        self.assertEqual(mock_joblib_load.call_count, 2)

    @patch('joblib.load')
    async def test_predict(self, mock_joblib_load):
        """测试预测"""
        # 设置joblib.load的返回值
        mock_joblib_load.side_effect = [self.mock_model, self.mock_woe_encoder]
        
        # 调用predict方法
        result = await self.prediction_service.predict(
            model_id="test_model",
            data={"feature1": 1.0, "feature2": 2.0}
        )
        
        # 验证结果
        self.assertEqual(result["prediction"], "健康")
        self.assertAlmostEqual(result["confidence"], 0.7)
        self.assertAlmostEqual(result["explanation"]["probability_healthy"], 0.7)
        self.assertAlmostEqual(result["explanation"]["probability_unhealthy"], 0.3)
        
        # 验证模型的predict_proba方法被调用
        self.mock_model.predict_proba.assert_called_once()
        
        # 验证WOE编码器的transform方法被调用
        self.mock_woe_encoder.transform.assert_called_once()

    @patch('joblib.load')
    async def test_predict_batch(self, mock_joblib_load):
        """测试批量预测"""
        # 设置joblib.load的返回值
        mock_joblib_load.side_effect = [self.mock_model, self.mock_woe_encoder]
        
        # 设置模型的批量预测行为
        self.mock_model.predict_proba.return_value = np.array([
            [0.3, 0.7],
            [0.6, 0.4]
        ])
        
        # 调用predict_batch方法
        results = await self.prediction_service.predict_batch(
            model_id="test_model",
            data=[
                {"feature1": 1.0, "feature2": 2.0},
                {"feature1": 3.0, "feature2": 4.0}
            ]
        )
        
        # 验证结果
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]["prediction"], "健康")
        self.assertEqual(results[1]["prediction"], "不健康")
        self.assertAlmostEqual(results[0]["confidence"], 0.7)
        self.assertAlmostEqual(results[1]["confidence"], 0.6)
        
        # 验证模型的predict_proba方法被调用
        self.mock_model.predict_proba.assert_called_once()
        
        # 验证WOE编码器的transform方法被调用
        self.mock_woe_encoder.transform.assert_called_once()

    def test_cache_management(self):
        """测试缓存管理"""
        # 清除缓存
        self.prediction_service.clear_cache()
        
        # 获取缓存信息
        cache_info = self.prediction_service.get_cache_info()
        
        # 验证缓存信息
        self.assertEqual(cache_info["hits"], 0)
        self.assertEqual(cache_info["misses"], 0)
        self.assertEqual(cache_info["maxsize"], 2)
        self.assertEqual(cache_info["currsize"], 0)


if __name__ == '__main__':
    unittest.main()