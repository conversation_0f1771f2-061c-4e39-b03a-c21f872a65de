"""
测试训练服务组件
"""
import asyncio
import json
import pandas as pd

from innovation_model_investigation.api.services.training_service import TrainingService
from innovation_model_investigation.api.utils.data_client import DataClient

async def test_training_service():
    """测试训练服务组件"""
    print("开始测试训练服务组件...")
    
    # 初始化训练服务
    training_service = TrainingService(models_dir="models")
    
    # 初始化数据客户端
    data_client = DataClient()
    
    # 测试数据集ID
    dataset_id = "test_dataset_001"
    
    try:
        # 1. 测试获取训练数据
        print("\n1. 测试获取训练数据")
        try:
            data = await training_service.fetch_training_data(dataset_id)
            print(f"成功获取训练数据: {len(data)} 行, {len(data.columns)} 列")
            print(f"数据集列名: {list(data.columns)[:5]}...")
        except Exception as e:
            print(f"获取训练数据失败: {str(e)}")
            # 使用本地数据作为备用
            print("使用本地数据作为备用...")
            data = pd.read_csv("data/enterprise_risk_sample_data.csv")
            print(f"成功加载本地数据: {len(data)} 行, {len(data.columns)} 列")
        
        # 2. 测试训练模型
        print("\n2. 测试训练模型")
        try:
            # 准备训练参数
            parameters = {
                "target_column": "label",
                "test_size": 0.3,
                "random_state": 42,
                "training_set_id": dataset_id,
            }
            
            # 执行训练
            model_id, result = await training_service.train_model(dataset_id, parameters)
            print(f"训练成功: 模型ID={model_id}, 版本={result['version']}")
            print(f"训练指标: {json.dumps(result['metrics'], indent=2)}")
            print(f"训练时间: {result['training_time']:.2f}秒")
        except Exception as e:
            print(f"训练模型失败: {str(e)}")
            model_id = None
        
        # 3. 测试获取模型信息
        if model_id:
            print(f"\n3. 测试获取模型信息: {model_id}")
            try:
                model_info = await training_service.get_model_info(model_id)
                print(f"成功获取模型信息:")
                print(f"  模型ID: {model_info.model_id}")
                print(f"  版本: {model_info.version}")
                print(f"  创建时间: {model_info.created_at}")
                print(f"  训练集ID: {model_info.training_set_id}")
                print(f"  指标: {json.dumps(model_info.metrics, indent=2)}")
            except Exception as e:
                print(f"获取模型信息失败: {str(e)}")
        
        # 4. 测试获取模型列表
        print("\n4. 测试获取模型列表")
        try:
            models = await training_service.list_models()
            print(f"成功获取模型列表: {len(models.get('models', []))} 个模型")
            for i, model in enumerate(models.get("models", [])[:3]):
                print(f"  模型 {i+1}: {model.get('model_id')}, 版本: {model.get('latest_version')}")
        except Exception as e:
            print(f"获取模型列表失败: {str(e)}")
        
        # 5. 测试获取训练统计信息
        print("\n5. 测试获取训练统计信息")
        try:
            stats = await training_service.get_training_stats()
            print(f"成功获取训练统计信息:")
            print(f"  总训练次数: {stats.get('total_trainings', 0)}")
            print(f"  模型数量: {stats.get('models_count', 0)}")
        except Exception as e:
            print(f"获取训练统计信息失败: {str(e)}")
        
        print("\n测试完成!")
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_training_service())