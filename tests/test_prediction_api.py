"""
测试预测API
"""
import asyncio
import json
import requests
import time
import pandas as pd
import os
from pathlib import Path

async def test_prediction_api():
    """测试预测API"""
    print("开始测试预测API...")
    
    # API基础URL
    base_url = "http://localhost:8000/api/v1"
    
    # 测试数据
    test_data = {
        "financial_ratio_current": 1.5,
        "financial_ratio_quick": 1.2,
        "financial_ratio_debt": 0.4,
        "financial_ratio_equity": 0.6,
        "financial_ratio_profit": 0.15,
        "financial_cash_flow": 0.08,
        "financial_turnover_asset": 0.7,
        "financial_turnover_inventory": 5.0,
        "financial_growth_revenue": 0.12,
        "financial_stability_ebitda": 0.09,
        "operation_market_share": 0.05,
        "operation_customer_concentration": 0.3,
        "operation_supplier_relationship": 0.8,
        "operation_product_diversity": 0.7,
        "operation_innovation_capability": 0.9,
        "operation_quality_management": 0.85,
        "operation_brand_value": 0.75,
        "operation_management_efficiency": 0.8,
        "operation_employee_stability": 0.7,
        "operation_digitalization_level": 0.6,
        "external_industry_position": 0.7,
        "external_policy_sensitivity": 0.4,
        "external_market_volatility": 0.5,
        "external_regulatory_compliance": 0.9,
        "external_environmental_impact": 0.8,
        "external_social_responsibility": 0.7,
        "external_technology_adaptation": 0.85,
        "external_economic_dependency": 0.6,
        "external_geographic_risk": 0.3,
        "external_competitive_pressure": 0.5,
        "establish_years": 10
    }
    
    try:
        # 1. 获取模型列表
        print("\n1. 获取模型列表")
        try:
            response = requests.get(f"{base_url}/models")
            response.raise_for_status()
            models = response.json().get("models", [])
            print(f"获取模型列表成功: {len(models)} 个模型")
            
            # 选择第一个模型进行测试
            if models:
                model_id = models[0].get("model_id")
                print(f"选择模型: {model_id}")
            else:
                print("未找到可用模型，将使用示例模型ID")
                model_id = "model_12345678"
        except Exception as e:
            print(f"获取模型列表失败: {str(e)}")
            print("使用示例模型ID")
            model_id = "model_12345678"
        
        # 2. 测试单条预测API
        print("\n2. 测试单条预测API")
        try:
            prediction_request = {
                "model_id": model_id,
                "data": test_data
            }
            
            response = requests.post(
                f"{base_url}/predict",
                json=prediction_request
            )
            response.raise_for_status()
            print(f"预测成功: {json.dumps(response.json(), indent=2)}")
        except Exception as e:
            print(f"预测失败: {str(e)}")
        
        # 3. 测试批量预测API
        print("\n3. 测试批量预测API")
        try:
            batch_data = [test_data.copy() for _ in range(5)]
            # 修改一些值，使结果更多样化
            for i, data in enumerate(batch_data):
                data["financial_ratio_current"] += i * 0.1
                data["operation_innovation_capability"] -= i * 0.05
            
            response = requests.post(
                f"{base_url}/predict/batch",
                json={
                    "model_id": model_id,
                    "data": batch_data
                }
            )
            response.raise_for_status()
            print(f"批量预测成功: {json.dumps(response.json(), indent=2)}")
        except Exception as e:
            print(f"批量预测失败: {str(e)}")
        
        # 4. 测试文件批量预测API
        print("\n4. 测试文件批量预测API")
        try:
            # 创建测试CSV文件
            test_df = pd.DataFrame(batch_data)
            temp_csv = Path("temp/test_prediction.csv")
            temp_csv.parent.mkdir(parents=True, exist_ok=True)
            test_df.to_csv(temp_csv, index=False)
            
            with open(temp_csv, "rb") as f:
                files = {"file": ("test_data.csv", f, "text/csv")}
                response = requests.post(
                    f"{base_url}/predict/file",
                    data={"model_id": model_id, "output_format": "json"},
                    files=files
                )
            
            if response.status_code == 200:
                # 保存返回的文件
                output_file = Path("temp/prediction_result.json")
                with open(output_file, "wb") as f:
                    f.write(response.content)
                print(f"文件批量预测成功，结果已保存到: {output_file}")
            else:
                print(f"文件批量预测失败: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"文件批量预测失败: {str(e)}")
        
        # 5. 测试预测统计API
        print("\n5. 测试预测统计API")
        try:
            response = requests.get(f"{base_url}/predict/stats")
            response.raise_for_status()
            print(f"获取预测统计成功: {json.dumps(response.json(), indent=2)}")
        except Exception as e:
            print(f"获取预测统计失败: {str(e)}")
        
        print("\n测试完成!")
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
    
    # 清理临时文件
    try:
        temp_csv = Path("temp/test_prediction.csv")
        if temp_csv.exists():
            os.remove(temp_csv)
    except Exception:
        pass

if __name__ == "__main__":
    # 等待服务启动
    print("等待服务启动...")
    time.sleep(2)
    
    asyncio.run(test_prediction_api())