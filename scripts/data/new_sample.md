几个问题：
1. python scripts/run_pipeline.py 报错  执行错误: 特征 tech_adj_ip_transformation 未找到WOE映射
Traceback (most recent call last):
  File "/Users/<USER>/code/langdong/investigation/innovation-model-investigation/scripts/run_pipeline.py", line 41, in main
    results = run_pipeline()
  File "/Users/<USER>/code/langdong/investigation/innovation-model-investigation/src/innovation_model_investigation/pipeline.py", line 129, in main
    X_test_woe = woe_encoder.transform(X_test)
  File "/Users/<USER>/code/langdong/investigation/innovation-model-investigation/src/innovation_model_investigation/features/woe_encoder.py", line 116, in transform
    raise ValueError(f"特征 {feature_name} 未找到WOE映射")
ValueError: 特征 tech_adj_ip_transformation 未找到WOE映射

1. python scripts/export_scorecard_rules.py  导出的评分卡，分箱范围和woe都有问题， 先解决上面那个问题，然后运行之后， 导出评分卡的分箱范围和woe值都是问题的，数据应该在 outpus/0.1.1/evaluation/Scorecard.md 里和 outputs/0.1.1/evaluation/scorecard_rules.json 里