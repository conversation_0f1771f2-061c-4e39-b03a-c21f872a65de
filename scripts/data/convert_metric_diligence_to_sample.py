#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将 goodcase-0715.csv 和 badcase_0715.csv 合并并转换为标准企业风险样本格式
- 字段名中文转英文code
- goodcase label=0, badcase label=1
- 生成 innovation_health_level 字段
- 生成 data_generate_time 字段
- enterprise_id 用公司名
- 输出到 data/enterprise_risk_sample_data.csv
"""
import pandas as pd
import numpy as np
from datetime import datetime

# 1. 读取数据
# 新增读取 goodcase 和 badcase
raw_good_path = "data/goodcase-0715.csv"
raw_bad_path = "data/badcase_0715.csv"
dict_path = "data/data_dictionary.csv"
out_path = "data/enterprise_risk_sample_data.csv"

good_df = pd.read_csv(raw_good_path)
bad_df = pd.read_csv(raw_bad_path)
dict_df = pd.read_csv(dict_path)

# 2. 分别加上 label 字段
good_df["label"] = 0
bad_df["label"] = 1

# 3. 合并数据
data_df = pd.concat([good_df, bad_df], ignore_index=True)

# 4. 构建中文名到英文code映射
dict_map = dict(zip(dict_df["indicator_name"], dict_df["indicator_code"]))

# 5. 字段重命名（只处理有映射的列）
rename_cols = {col: dict_map[col] for col in data_df.columns if col in dict_map}
converted_df = data_df.rename(columns=rename_cols)

# 6. enterprise_id 字段（直接从原始 data_df 取，避免重命名后丢失）
name_col = "企业名称"
if name_col not in data_df.columns:
    raise ValueError("原始数据缺少“企业名称”列，请检查数据文件！")
converted_df["enterprise_id"] = data_df[name_col]
converted_df["enterprise_name"] = data_df[name_col]

# 处理不同类型的缺失值填充逻辑
# 1. 找出binary类型的列
binary_cols = dict_df.loc[
    dict_df["formula_type"] == "binary", "indicator_code"
].tolist()

# 2. 针对binary列，缺失值填充为0
for col in binary_cols:
    if col in converted_df.columns:
        converted_df[col] = converted_df[col].fillna(0)
# 3. 针对 percentage 类型，检查范围并可选填充
percentage_cols = dict_df.loc[
    dict_df["formula_type"] == "percentage", "indicator_code"
].tolist()
for col in percentage_cols:
    if col in converted_df.columns:
        # 检查是否有超出0-1范围的值
        if (converted_df[col].dropna() > 1).any():
            print(f"⚠️ 警告：{col} 存在超出0-1范围的值，请检查原始数据！")
        # 可选：对缺失值填充为0或其他策略
        # converted_df[col] = converted_df[col].fillna(0)

# 新增：oper_equity_change_frequency、oper_key_personnel_change 缺失值填充为0
for col in ["oper_equity_change_frequency", "oper_key_personnel_change"]:
    if col in converted_df.columns:
        converted_df[col] = converted_df[col].fillna(0)


# 7. innovation_health_level 生成
def calc_level(score):
    try:
        score = float(score)
        if score >= 80:
            return "优秀"
        elif score >= 65:
            return "良好"
        elif score >= 50:
            return "一般"
        elif score >= 35:
            return "较差"
        else:
            return "差"
    except Exception:
        return "差"


# 选取综合评分列
if "comprehensive_score" in converted_df.columns:
    score_col = "comprehensive_score"
elif "综合评分" in converted_df.columns:
    score_col = "综合评分"
else:
    score_col = None

if score_col:
    converted_df["innovation_health_level"] = converted_df[score_col].apply(calc_level)
else:
    converted_df["innovation_health_level"] = "差"

# 8. data_generate_time
converted_df["data_generate_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# 9. 字段顺序对齐（参考 data_dictionary.csv 字段顺序）
# 读取标准字段顺序
indicator_codes = dict_df["indicator_code"].tolist()
# 新生成的字段，按业务需求顺序排列
extra_cols = [
    # "enterprise_id",
    # "enterprise_name",
    # "label",
    "innovation_health_level",
    # "data_generate_time",
]
# 合并为最终输出顺序
sample_cols = extra_cols + indicator_codes

# 保证所有需要的列都在，缺失的补空
for col in sample_cols:
    if col not in converted_df.columns:
        converted_df[col] = np.nan

# 只对指标列做缺失值填充
indicator_cols = [
    dict_map[name] for name in dict_map if dict_map[name] in converted_df.columns
]
# 这里不填充 NaN，保留原始数据
# converted_df[indicator_cols] = converted_df[indicator_cols].fillna(-999)

# 按标准顺序输出
converted_df = converted_df[sample_cols]

# 10. 保存
converted_df.to_csv(out_path, index=False, encoding="utf-8-sig")
print(f"✅ 转换完成，已保存到 {out_path}")
