#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业科创健康行评估样例数据生成器

基于真实的科创健康行指标体系生成企业样本数据，用于模型训练
严格按照metric-definition.csv中的指标定义生成数据
- 百分比类型指标：0-1之间的小数，可能为null
- 是/否类型指标：0或1
- 计数类型指标：整数，可能为null
"""

import pandas as pd
import numpy as np
from datetime import datetime
import random
from pathlib import Path

# 设置随机种子确保结果可复现
np.random.seed(42)
random.seed(42)

def generate_percentage_indicator(label, discrimination_level='medium', missing_rate=0.15):
    """
    生成百分比类型指标（0-1之间的小数）

    Args:
        label: 企业标签，0=好企业，1=坏企业
        discrimination_level: 区分度等级 ('high', 'medium', 'low', 'none')
        missing_rate: 缺失值比例

    Returns:
        指标值或None（表示缺失）
    """
    # 随机决定是否为缺失值
    if np.random.random() < missing_rate:
        return None

    if discrimination_level == 'high':
        # 高区分度：标签影响很大
        if label == 0:  # 好企业
            value = np.random.beta(3, 1.5)  # 偏向高值
        else:  # 坏企业
            value = np.random.beta(1.5, 3)  # 偏向低值
    elif discrimination_level == 'medium':
        # 中区分度：标签有一定影响
        if label == 0:  # 好企业
            value = np.random.beta(2.5, 2)  # 略偏向高值
        else:  # 坏企业
            value = np.random.beta(2, 2.5)  # 略偏向低值
    elif discrimination_level == 'low':
        # 低区分度：标签影响很小
        if label == 0:  # 好企业
            value = np.random.beta(2.2, 2.2)  # 轻微偏向高值
        else:  # 坏企业
            value = np.random.beta(2.2, 2.2)  # 几乎相同分布
    else:  # 'none'
        # 无区分度：完全随机
        value = np.random.beta(2, 2)  # 均匀分布

    return np.clip(value, 0, 1)

def generate_binary_indicator(label, discrimination_level='medium', positive_prob_good=0.3, positive_prob_bad=0.1):
    """
    生成二元指标（0或1）

    Args:
        label: 企业标签，0=好企业，1=坏企业
        discrimination_level: 区分度等级
        positive_prob_good: 好企业为1的概率
        positive_prob_bad: 坏企业为1的概率

    Returns:
        0或1
    """
    if discrimination_level == 'high':
        prob = positive_prob_good if label == 0 else positive_prob_bad
    elif discrimination_level == 'medium':
        # 中等区分度，缩小差距
        avg_prob = (positive_prob_good + positive_prob_bad) / 2
        prob = (positive_prob_good + avg_prob) / 2 if label == 0 else (positive_prob_bad + avg_prob) / 2
    elif discrimination_level == 'low':
        # 低区分度，进一步缩小差距
        avg_prob = (positive_prob_good + positive_prob_bad) / 2
        prob = avg_prob * 1.1 if label == 0 else avg_prob * 0.9
    else:  # 'none'
        # 无区分度：完全随机
        prob = (positive_prob_good + positive_prob_bad) / 2

    return 1 if np.random.random() < prob else 0

def generate_count_indicator(label, discrimination_level='medium', max_count=10, missing_rate=0.1):
    """
    生成计数类型指标

    Args:
        label: 企业标签，0=好企业，1=坏企业
        discrimination_level: 区分度等级
        max_count: 最大计数值
        missing_rate: 缺失值比例

    Returns:
        整数值或None（表示缺失）
    """
    # 随机决定是否为缺失值
    if np.random.random() < missing_rate:
        return None

    if discrimination_level == 'high':
        if label == 0:  # 好企业
            # 使用泊松分布，好企业倾向于更高的计数
            value = np.random.poisson(max_count * 0.7)
        else:  # 坏企业
            value = np.random.poisson(max_count * 0.3)
    elif discrimination_level == 'medium':
        if label == 0:  # 好企业
            value = np.random.poisson(max_count * 0.6)
        else:  # 坏企业
            value = np.random.poisson(max_count * 0.4)
    elif discrimination_level == 'low':
        if label == 0:  # 好企业
            value = np.random.poisson(max_count * 0.55)
        else:  # 坏企业
            value = np.random.poisson(max_count * 0.45)
    else:  # 'none'
        value = np.random.poisson(max_count * 0.5)

    return min(value, max_count)

def generate_enterprise_sample_data(n_samples=2000, output_dir="data"):
    """
    生成企业科创健康行评估样例数据
    严格按照metric-definition.csv中的指标定义生成数据

    Args:
        n_samples: 生成样本数量，默认2000
        output_dir: 输出目录
    """

    # 创建输出目录
    Path(output_dir).mkdir(exist_ok=True)

    # 生成标签：65%好企业，35%坏企业
    labels = np.random.choice([0, 1], size=n_samples, p=[0.65, 0.35])

    # 生成企业基本信息
    enterprises = []
    for i in range(n_samples):
        enterprise = {
            'enterprise_id': f'ENT_{i+1:06d}',
            'enterprise_name': f'企业_{i+1}号',
            'industry_code': random.choice(['01', '02', '03', '04', '05']),  # 5个行业
            'region_code': random.choice(['A', 'B', 'C', 'D']),  # 4个地区
            'enterprise_size': random.choice(['小微', '小型', '中型', '大型']),
            'establish_years': random.randint(1, 50),
            'label': labels[i]  # 预先确定的标签
        }
        enterprises.append(enterprise)
    
    # 生成样本数据
    data_rows = []

    # 定义指标的区分度等级和数据类型
    # 根据metric-definition.csv中的公式和备注分析得出
    indicator_config = {
        # 百分比类型指标（0-1之间的小数，可能为null）
        '发明专利申请占比': {'type': 'percentage', 'discrimination': 'high', 'missing_rate': 0.15},
        '发明专利申请驳回率': {'type': 'percentage', 'discrimination': 'high', 'missing_rate': 0.20},
        '发明专利申请稳定性': {'type': 'percentage', 'discrimination': 'medium', 'missing_rate': 0.25},
        '发明专利授权率': {'type': 'percentage', 'discrimination': 'high', 'missing_rate': 0.15},
        '发明专利授权维持率': {'type': 'percentage', 'discrimination': 'high', 'missing_rate': 0.20},
        '发明专利授权稳定性': {'type': 'percentage', 'discrimination': 'medium', 'missing_rate': 0.25},
        '发明专利授权排名': {'type': 'percentage', 'discrimination': 'medium', 'missing_rate': 0.30},
        '软件著作权排名': {'type': 'percentage', 'discrimination': 'low', 'missing_rate': 0.35},
        '发明专利申请集中度': {'type': 'percentage', 'discrimination': 'low', 'missing_rate': 0.25},
        '外购专利占比': {'type': 'percentage', 'discrimination': 'low', 'missing_rate': 0.30},
        '人才招聘稳定性': {'type': 'percentage', 'discrimination': 'medium', 'missing_rate': 0.20},
        '注册资本实缴比例': {'type': 'percentage', 'discrimination': 'high', 'missing_rate': 0.10},

        # 二元指标（0或1）
        '发明专利申请连续性': {'type': 'binary', 'discrimination': 'medium', 'pos_prob_good': 0.7, 'pos_prob_bad': 0.3},
        '发明专利流出': {'type': 'binary', 'discrimination': 'medium', 'pos_prob_good': 0.1, 'pos_prob_bad': 0.3},
        '有效PCT国际专利': {'type': 'binary', 'discrimination': 'low', 'pos_prob_good': 0.3, 'pos_prob_bad': 0.1},
        '知识产权质押': {'type': 'binary', 'discrimination': 'low', 'pos_prob_good': 0.2, 'pos_prob_bad': 0.1},
        '知识产权转化': {'type': 'binary', 'discrimination': 'none', 'pos_prob_good': 0.15, 'pos_prob_bad': 0.15},
        '科技成果': {'type': 'binary', 'discrimination': 'none', 'pos_prob_good': 0.25, 'pos_prob_bad': 0.25},
        '员工持股平台': {'type': 'binary', 'discrimination': 'low', 'pos_prob_good': 0.3, 'pos_prob_bad': 0.2},
        '荣誉取消': {'type': 'binary', 'discrimination': 'medium', 'pos_prob_good': 0.05, 'pos_prob_bad': 0.2},
        '累计减资': {'type': 'binary', 'discrimination': 'medium', 'pos_prob_good': 0.1, 'pos_prob_bad': 0.3},
        '股权结构': {'type': 'binary', 'discrimination': 'medium', 'pos_prob_good': 0.1, 'pos_prob_bad': 0.4},
        '关联方异动': {'type': 'binary', 'discrimination': 'low', 'pos_prob_good': 0.1, 'pos_prob_bad': 0.2},
        '主体及关联方经营状态': {'type': 'binary', 'discrimination': 'medium', 'pos_prob_good': 0.05, 'pos_prob_bad': 0.25},
        '主体及关联方吊销': {'type': 'binary', 'discrimination': 'high', 'pos_prob_good': 0.02, 'pos_prob_bad': 0.15},
        '主体及关联方被执行限高': {'type': 'binary', 'discrimination': 'high', 'pos_prob_good': 0.03, 'pos_prob_bad': 0.20},
        '主体及关联方涉及金融诉讼': {'type': 'binary', 'discrimination': 'high', 'pos_prob_good': 0.05, 'pos_prob_bad': 0.25},
        '主体及关联方环保处罚': {'type': 'binary', 'discrimination': 'medium', 'pos_prob_good': 0.08, 'pos_prob_bad': 0.20},
        '主体及关联方欠税': {'type': 'binary', 'discrimination': 'high', 'pos_prob_good': 0.02, 'pos_prob_bad': 0.18},

        # 计数类型指标
        '股权融资': {'type': 'count', 'discrimination': 'medium', 'max_count': 2, 'missing_rate': 0.1},
        '企业荣誉': {'type': 'count', 'discrimination': 'high', 'max_count': 10, 'missing_rate': 0.15},
        '关键人员变动': {'type': 'count', 'discrimination': 'medium', 'max_count': 5, 'missing_rate': 0.1},
        '股权变更频率': {'type': 'count', 'discrimination': 'medium', 'max_count': 6, 'missing_rate': 0.1},
    }

    for ent in enterprises:
        row = ent.copy()
        label = ent['label']  # 获取预设标签

        # 生成所有指标
        indicators = {}

        for indicator_name, config in indicator_config.items():
            if config['type'] == 'percentage':
                # 百分比类型指标（0-1之间的小数）
                value = generate_percentage_indicator(
                    label,
                    config['discrimination'],
                    config['missing_rate']
                )
                indicators[indicator_name] = value

            elif config['type'] == 'binary':
                # 二元指标（0或1）
                value = generate_binary_indicator(
                    label,
                    config['discrimination'],
                    config['pos_prob_good'],
                    config['pos_prob_bad']
                )
                indicators[indicator_name] = value

            elif config['type'] == 'count':
                # 计数类型指标
                value = generate_count_indicator(
                    label,
                    config['discrimination'],
                    config['max_count'],
                    config['missing_rate']
                )
                indicators[indicator_name] = value

        # 将所有指标添加到行数据中
        row.update(indicators)

        # 计算简化的综合评分（基于标签和一些关键指标）
        # 这里简化处理，主要目的是生成合理的评分分布

        # 基础评分：根据标签设定基础分数
        if label == 0:  # 好企业
            base_score = np.random.normal(70, 15)  # 平均70分，标准差15
        else:  # 坏企业
            base_score = np.random.normal(45, 15)  # 平均45分，标准差15

        # 根据一些关键指标调整评分
        score_adjustments = 0

        # 专利相关指标调整
        if indicators.get('发明专利申请占比') is not None and indicators['发明专利申请占比'] > 0.7:
            score_adjustments += 5
        if indicators.get('发明专利授权率') is not None and indicators['发明专利授权率'] > 0.8:
            score_adjustments += 5

        # 风险指标调整
        if indicators.get('主体及关联方被执行限高') == 1:
            score_adjustments -= 10
        if indicators.get('主体及关联方涉及金融诉讼') == 1:
            score_adjustments -= 8
        if indicators.get('主体及关联方欠税') == 1:
            score_adjustments -= 8

        # 正面指标调整
        if indicators.get('企业荣誉') is not None and indicators['企业荣誉'] > 5:
            score_adjustments += 3
        if indicators.get('有效PCT国际专利') == 1:
            score_adjustments += 3

        comprehensive_score = base_score + score_adjustments
        comprehensive_score = max(0, min(100, comprehensive_score))

        # 确定科创健康行等级
        if comprehensive_score >= 80:
            innovation_health_level = "优秀"
        elif comprehensive_score >= 65:
            innovation_health_level = "良好"
        elif comprehensive_score >= 50:
            innovation_health_level = "一般"
        elif comprehensive_score >= 35:
            innovation_health_level = "较差"
        else:
            innovation_health_level = "差"

        # 添加计算结果到行数据
        row.update({
            'comprehensive_score': round(comprehensive_score, 2),
            'innovation_health_level': innovation_health_level
        })

        data_rows.append(row)

    # 创建DataFrame
    df = pd.DataFrame(data_rows)

    # 添加数据生成时间戳
    df['data_generate_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # 保存到CSV文件
    output_file = Path(output_dir) / "enterprise_risk_sample_data.csv"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')

    # 生成数据概要报告
    print("=" * 60)
    print("🎯 企业科创健康行评估样例数据生成完成")
    print("=" * 60)
    print(f"📊 样本数量: {len(df):,} 个企业")
    print(f"📈 指标数量: {len(indicator_config)} 个真实指标")
    print(f"📁 输出路径: {output_file}")
    print()

    # 标签分布统计
    label_stats = df['label'].value_counts()
    print("📋 标签分布:")
    print(f"   好企业 (0): {label_stats[0]:,} 个 ({label_stats[0]/len(df)*100:.1f}%)")
    print(f"   坏企业 (1): {label_stats[1]:,} 个 ({label_stats[1]/len(df)*100:.1f}%)")
    print()

    # 科创健康行等级分布
    level_stats = df['innovation_health_level'].value_counts()
    print("🏆 科创健康行等级分布:")
    for level, count in level_stats.items():
        print(f"   {level}: {count:,} 个 ({count/len(df)*100:.1f}%)")
    print()

    # 企业规模分布
    size_stats = df['enterprise_size'].value_counts()
    print("🏢 企业规模分布:")
    for size, count in size_stats.items():
        print(f"   {size}: {count:,} 个 ({count/len(df)*100:.1f}%)")
    print()

    # 综合评分统计
    print("📊 综合评分统计:")
    print(f"   平均分: {df['comprehensive_score'].mean():.1f}")
    print(f"   标准差: {df['comprehensive_score'].std():.1f}")
    print(f"   最高分: {df['comprehensive_score'].max():.1f}")
    print(f"   最低分: {df['comprehensive_score'].min():.1f}")
    print()

    # 数据类型统计
    print("📋 数据类型统计:")
    percentage_indicators = [k for k, v in indicator_config.items() if v['type'] == 'percentage']
    binary_indicators = [k for k, v in indicator_config.items() if v['type'] == 'binary']
    count_indicators = [k for k, v in indicator_config.items() if v['type'] == 'count']

    print(f"   百分比指标: {len(percentage_indicators)} 个")
    print(f"   二元指标: {len(binary_indicators)} 个")
    print(f"   计数指标: {len(count_indicators)} 个")
    print()

    # 缺失值统计
    print("📋 缺失值统计:")
    for indicator in percentage_indicators + count_indicators:
        if indicator in df.columns:
            missing_count = df[indicator].isna().sum()
            missing_rate = missing_count / len(df) * 100
            print(f"   {indicator}: {missing_count} 个 ({missing_rate:.1f}%)")
    print()

    print("✅ 数据生成完成！")
    print("💡 特点：")
    print("   - 百分比指标为0-1之间的小数，可能包含null值")
    print("   - 二元指标为0或1，表示是/否")
    print("   - 计数指标为整数，可能包含null值")
    print("   - null值在真实场景中是有意义的，表示该指标无法获取或不适用")
    print("=" * 60)

    return df

if __name__ == "__main__":
    # 生成样例数据
    df = generate_enterprise_sample_data(n_samples=2000)
