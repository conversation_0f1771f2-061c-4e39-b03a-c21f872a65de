#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Compare Scorecard Performance
自动查找outputs/<latest_version>/evaluation/model_scorecard_compare.csv，
对比人工加权总分与模型分数对主观标签的判别力。
"""

import sys
import os
import glob
import pandas as pd
import numpy as np
from sklearn.metrics import roc_auc_score, roc_curve, accuracy_score, confusion_matrix, f1_score, precision_score, recall_score
import matplotlib.pyplot as plt
import argparse
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei']  # 设置常见中文字体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示为方块的问题

def find_versions_with_compare_data():
    """查找所有有对比数据的版本"""
    outputs_dir = "outputs"
    # 获取所有版本号文件夹
    version_dirs = [d for d in os.listdir(outputs_dir) if os.path.isdir(os.path.join(outputs_dir, d)) and not d.startswith('.')]
    # 过滤掉非版本号文件夹
    version_dirs = [d for d in version_dirs if any(c.isdigit() for c in d)]
    if not version_dirs:
        print("❌ 未找到任何版本输出目录！")
        return []

    # 按版本号排序（假设语义化版本）
    def version_key(v):
        try:
            if '_enhanced' in v:
                base_v = v.replace('_enhanced', '')
                parts = [int(x) for x in base_v.split('.')]
                return parts + [1]  # enhanced版本排在后面
            else:
                parts = [int(x) for x in v.split('.')]
                return parts + [0]
        except:
            return [0, 0, 0, 0]

    version_dirs.sort(key=version_key, reverse=True)

    # 查找有对比数据的版本
    valid_versions = []
    for v in version_dirs:
        # 优先从 baseline 目录查找
        baseline_path = os.path.join(outputs_dir, v, "baseline", "model_scorecard_compare.csv")
        eval_path = os.path.join(outputs_dir, v, "evaluation", "model_scorecard_compare.csv")

        if os.path.exists(baseline_path):
            valid_versions.append((baseline_path, v, 'baseline'))
        elif os.path.exists(eval_path):
            valid_versions.append((eval_path, v, 'evaluation'))

    return valid_versions

def find_latest_compare_csv():
    """查找最新版本的对比数据"""
    valid_versions = find_versions_with_compare_data()
    if not valid_versions:
        print("❌ 未找到任何 compare.csv 文件！")
        return None, None

    # 返回最新版本
    compare_path, version, source = valid_versions[0]
    print(f"📂 使用版本 {version} 的对比数据 (来源: {source})")
    return compare_path, version

def calc_ks(y_true, y_score):
    fpr, tpr, _ = roc_curve(y_true, y_score)
    ks = max(tpr - fpr)
    return ks

def evaluate_score(y_true, score, threshold=None):
    auc = roc_auc_score(y_true, score)
    ks = calc_ks(y_true, score)
    if threshold is None:
        fpr, tpr, thresholds = roc_curve(y_true, score)
        youden = tpr - fpr
        best_idx = np.argmax(youden)
        threshold = thresholds[best_idx]
    y_pred = (score >= threshold).astype(int)
    acc = accuracy_score(y_true, y_pred)
    f1 = f1_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred)
    recall = recall_score(y_true, y_pred)
    cm = confusion_matrix(y_true, y_pred)
    tn, fp, fn, tp = cm.ravel()
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    return {
        "AUC": auc,
        "KS": ks,
        "Accuracy": acc,
        "F1": f1,
        "Precision": precision,
        "Recall": recall,
        "Specificity": specificity,
        "Threshold": threshold,
        "ConfusionMatrix": cm,
        "TP": tp, "FP": fp, "TN": tn, "FN": fn
    }

def print_eval_result(name, result, file=None):
    lines = [
        f"\n📊 {name} 判别力评估:",
        f"  AUC:        {result['AUC']:.4f}",
        f"  KS值:       {result['KS']:.4f}",
        f"  准确率:     {result['Accuracy']:.4f}",
        f"  精确率:     {result['Precision']:.4f}",
        f"  召回率:     {result['Recall']:.4f}",
        f"  特异性:     {result['Specificity']:.4f}",
        f"  F1分数:     {result['F1']:.4f}",
        f"  最优阈值:   {result['Threshold']:.4f}",
        f"  混淆矩阵:   TN={result['TN']}  FP={result['FP']}  FN={result['FN']}  TP={result['TP']}"
    ]
    for line in lines:
        print(line)
        if file:
            file.write(line + "\n")

def create_comparison_table(all_results):
    """创建对比表格，将所有结果汇总到一个表格中"""
    # 准备表格数据
    table_data = []
    metrics = ['AUC', 'KS', 'Accuracy', 'Precision', 'Recall', 'Specificity', 'F1', 'Threshold']

    for subset in ['all', 'train', 'test']:
        subset_names = {'all': '全量样本', 'train': '训练集', 'test': '测试集'}
        subset_name = subset_names[subset]

        if subset in all_results:
            # 人工分数行
            if all_results[subset]['trad']:
                row_trad = [f"{subset_name}-人工分数"]
                for metric in metrics:
                    row_trad.append(f"{all_results[subset]['trad'][metric]:.4f}")
                table_data.append(row_trad)

            # 模型分数行
            if all_results[subset]['model']:
                row_model = [f"{subset_name}-模型分数"]
                for metric in metrics:
                    row_model.append(f"{all_results[subset]['model'][metric]:.4f}")
                table_data.append(row_model)

    return table_data, ['评估对象'] + metrics

def plot_roc(y_true, score, label, color):
    fpr, tpr, _ = roc_curve(y_true, score)
    auc = roc_auc_score(y_true, score)
    plt.plot(fpr, tpr, label=f"{label} (AUC={auc:.3f})", color=color)

def evaluate_all_subsets(df):
    """评估所有数据子集的性能"""
    results = {}

    for subset, subset_name in zip(['all', 'train', 'test'], ['全量样本', '训练集', '测试集']):
        if 'is_test' in df.columns:
            if subset == 'test':
                df_sub = df[df['is_test'] == 1]
            elif subset == 'train':
                df_sub = df[df['is_test'] == 0]
            else:
                df_sub = df
        else:
            df_sub = df

        if len(df_sub) == 0:
            print(f"⚠️ {subset_name} 数据为空，跳过")
            continue

        y_true = df_sub['label']
        print(f"评估对象：{subset_name} ({len(df_sub)} 个样本)")

        # 检查必要字段
        if 'traditional_total_score' not in df_sub.columns:
            print("❌ 缺少 traditional_total_score 字段！")
            continue

        # 评估传统分数
        traditional_score = -df_sub['traditional_total_score']
        result_trad = evaluate_score(y_true, traditional_score)
        print_eval_result("人工加权总分", result_trad)

        # 评估模型分数
        result_model = None
        if 'model_score' in df_sub.columns:
            model_score = df_sub['model_score']
            result_model = evaluate_score(y_true, model_score)
            print_eval_result("模型分数", result_model)

        results[subset] = {
            'trad': result_trad,
            'model': result_model,
            'y_true': y_true,
            'model_score': df_sub.get('model_score', None),
            'traditional_score': traditional_score
        }

    return results

def save_comparison_report(results, compare_path, version):
    """保存对比报告"""
    # 创建 compare_traditional 目录
    outputs_dir = os.path.dirname(os.path.dirname(compare_path))  # 回到版本目录
    compare_traditional_dir = os.path.join(outputs_dir, "analyzed_data/compare_traditional")
    os.makedirs(compare_traditional_dir, exist_ok=True)

    report_path = os.path.join(compare_traditional_dir, "compare_with_traditional.md")
    with open(report_path, "w", encoding="utf-8") as f:
        f.write(f"# 评分卡性能对比汇总报告\n\n")
        f.write(f"**版本**: {version}  \n")
        f.write(f"**生成时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}  \n\n")

        # 创建对比表格
        table_data, headers = create_comparison_table(results)

        if table_data:
            f.write("## 📊 性能指标对比表格\n\n")

            # 写入Markdown表格
            # 表头
            f.write("| " + " | ".join(headers) + " |\n")
            f.write("| " + " | ".join(["---"] * len(headers)) + " |\n")

            # 数据行
            for row in table_data:
                f.write("| " + " | ".join(str(cell) for cell in row) + " |\n")

            f.write("\n")

        # 添加详细分析
        f.write("## 📈 详细分析\n\n")

        # 总体性能对比
        f.write("### 🎯 核心指标对比\n\n")
        for subset, subset_name in zip(['all', 'train', 'test'], ['全量样本', '训练集', '测试集']):
            if subset in results and results[subset]['trad'] and results[subset]['model']:
                f.write(f"#### {subset_name}\n\n")

                trad_auc = results[subset]['trad']['AUC']
                model_auc = results[subset]['model']['AUC']
                auc_diff = model_auc - trad_auc
                auc_status = '🟢 模型更优' if auc_diff > 0 else '🔴 传统更优' if auc_diff < 0 else '🟡 相当'

                trad_ks = results[subset]['trad']['KS']
                model_ks = results[subset]['model']['KS']
                ks_diff = model_ks - trad_ks
                ks_status = '🟢 模型更优' if ks_diff > 0 else '🔴 传统更优' if ks_diff < 0 else '🟡 相当'

                f.write(f"- **AUC提升**: {auc_diff:+.4f} ({auc_status})\n")
                f.write(f"- **KS值提升**: {ks_diff:+.4f} ({ks_status})\n")
                f.write(f"- **样本数量**: {len(results[subset]['y_true'])} 个\n\n")

        # 添加模型解释
        f.write("### 💡 结果解释\n\n")
        f.write("- **AUC (Area Under Curve)**: 衡量模型整体判别能力，值越大越好\n")
        f.write("- **KS值**: 衡量模型区分好坏样本的能力，通常0.2-0.6为良好\n")
        f.write("- **精确率**: 预测为坏的样本中真正为坏的比例\n")
        f.write("- **召回率**: 真正为坏的样本中被正确识别的比例\n")
        f.write("- **F1分数**: 精确率和召回率的调和平均数\n\n")

        # 添加建议
        f.write("### 🚀 优化建议\n\n")
        best_subset = None
        best_auc_diff = -1
        for subset in ['all', 'train', 'test']:
            if subset in results and results[subset]['trad'] and results[subset]['model']:
                auc_diff = results[subset]['model']['AUC'] - results[subset]['trad']['AUC']
                if auc_diff > best_auc_diff:
                    best_auc_diff = auc_diff
                    best_subset = subset

        if best_subset and best_auc_diff > 0:
            f.write(f"- ✅ 模型在{['全量样本', '训练集', '测试集'][['all', 'train', 'test'].index(best_subset)]}上表现最佳\n")
            f.write(f"- 📊 建议重点关注模型在实际业务中的应用效果\n")
        elif best_auc_diff <= 0:
            f.write("- ⚠️ 模型性能未显著优于传统方法，建议：\n")
            f.write("  - 检查特征工程是否充分\n")
            f.write("  - 考虑增加更多有效特征\n")
            f.write("  - 尝试其他建模方法\n")

        f.write("\n---\n")
        f.write("*报告由 compare_scorecard_performance.py 自动生成*\n")

    print(f"📄 统一对比报告已保存到: {report_path}")
    return report_path

def compare_multiple_versions():
    """对比多个版本（包括enhanced版本）"""
    valid_versions = find_versions_with_compare_data()
    if not valid_versions:
        print("❌ 未找到任何对比数据！")
        return

    print(f"📂 发现 {len(valid_versions)} 个版本的对比数据")

    # 对每个版本进行分析
    all_version_results = {}

    for compare_path, version, source in valid_versions:
        print(f"\n🔍 分析版本: {version}")
        print("-" * 50)

        df = pd.read_csv(compare_path)
        results = evaluate_all_subsets(df)
        all_version_results[version] = {
            'results': results,
            'compare_path': compare_path,
            'source': source
        }

        # 保存单版本报告
        save_comparison_report(results, compare_path, version)

        # 绘制ROC曲线
        save_roc_plot(results, version, compare_path)

    # 生成多版本对比摘要
    save_multi_version_summary(all_version_results)

    return all_version_results

def save_roc_plot(results, version, compare_path):
    """保存ROC曲线图"""
    plt.figure(figsize=(10,7))
    colors = {'all':'#2ecc71', 'train':'#3498db', 'test':'#e74c3c'}
    linestyles = {'model':'-', 'trad':'--'}

    for subset, subset_name in zip(['all', 'train', 'test'], ['全量样本', '训练集', '测试集']):
        if subset in results:
            for score_type, score_label in zip(['model', 'trad'], ['模型分数', '人工分数']):
                if results[subset][score_type] is not None:
                    y_true = results[subset]['y_true']
                    if score_type == 'model':
                        score = results[subset]['model_score']
                    else:
                        score = results[subset]['traditional_score']

                    if score is not None and hasattr(score, '__len__') and len(score) == len(y_true):
                        fpr, tpr, _ = roc_curve(y_true, score)
                        auc = roc_auc_score(y_true, score)
                        plt.plot(fpr, tpr, linestyle=linestyles[score_type], color=colors[subset],
                                 label=f"{score_label}-{subset_name} (AUC={auc:.3f})")

    plt.plot([0,1],[0,1],'k--',alpha=0.5)
    plt.xlabel("False Positive Rate")
    plt.ylabel("True Positive Rate")
    plt.title(f"模型分数与人工分数ROC曲线对比 (v{version})")
    plt.legend()
    plt.tight_layout()

    # 保存到 compare_traditional 目录
    outputs_dir = os.path.dirname(os.path.dirname(compare_path))
    compare_traditional_dir = os.path.join(outputs_dir, "analyzed_data/compare_traditional")
    os.makedirs(compare_traditional_dir, exist_ok=True)

    roc_path = os.path.join(compare_traditional_dir, f"roc_comparison_{version}.png")
    plt.savefig(roc_path, dpi=120)
    plt.close()
    print(f"📈 ROC曲线对比图已保存到: {roc_path}")

def save_multi_version_summary(all_version_results):
    """保存多版本对比摘要"""
    if not all_version_results:
        return

    # 使用第一个版本的目录作为输出目录
    first_version = list(all_version_results.keys())[0]
    first_path = all_version_results[first_version]['compare_path']
    outputs_dir = os.path.dirname(os.path.dirname(first_path))
    compare_traditional_dir = os.path.join(outputs_dir, "analyzed_data/compare_traditional")
    os.makedirs(compare_traditional_dir, exist_ok=True)

    summary_path = os.path.join(compare_traditional_dir, "multi_version_comparison_summary.md")

    with open(summary_path, "w", encoding="utf-8") as f:
        f.write("# 多版本评分卡性能对比摘要\n\n")
        f.write(f"**生成时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"**对比版本**: {', '.join(all_version_results.keys())}\n\n")

        # 版本对比表格
        f.write("## 📊 版本性能对比\n\n")
        f.write("| 版本 | 测试集模型AUC | 测试集传统AUC | AUC提升 | 数据来源 |\n")
        f.write("|------|---------------|---------------|---------|----------|\n")

        for version, data in all_version_results.items():
            results = data['results']
            source = data['source']

            if 'test' in results and results['test']['model'] and results['test']['trad']:
                model_auc = results['test']['model']['AUC']
                trad_auc = results['test']['trad']['AUC']
                auc_diff = model_auc - trad_auc
                f.write(f"| {version} | {model_auc:.4f} | {trad_auc:.4f} | {auc_diff:+.4f} | {source} |\n")

        f.write("\n## 💡 分析建议\n\n")
        f.write("- 查看各版本的详细报告了解具体性能差异\n")
        f.write("- 关注enhanced版本与基础版本的性能对比\n")
        f.write("- 建议选择测试集AUC最高的版本用于生产环境\n")

        f.write("\n---\n*报告由 model_compare_traditional.py 自动生成*\n")

    print(f"📄 多版本对比摘要已保存到: {summary_path}")

def main():
    parser = argparse.ArgumentParser(description='评分卡判别力对比')
    parser.add_argument('--show', action='store_true', help='是否弹窗显示ROC曲线')
    parser.add_argument('--single', action='store_true', help='只分析最新版本')
    args = parser.parse_args()

    print("\n=== 对比人工打分和模型的效果 ===\n")

    if args.single:
        # 只分析最新版本
        compare_path, version = find_latest_compare_csv()
        if not compare_path:
            print("❌ compare.csv 未找到，无法对比分析！")
            return

        print(f"读取对比数据: {compare_path}")
        df = pd.read_csv(compare_path)
        results = evaluate_all_subsets(df)
        save_comparison_report(results, compare_path, version)
        save_roc_plot(results, version, compare_path)
    else:
        # 分析所有版本
        compare_multiple_versions()

    print("\n✅ 对比分析完成！")

if __name__ == "__main__":
    main()