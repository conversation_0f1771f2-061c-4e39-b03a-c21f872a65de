#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强特征模型训练流水线
复用 run_pipeline.py 的完整逻辑，但使用增强特征数据
输出到 outputs/<version>_enhanced/ 目录，保持与原pipeline相同的文件结构
"""

import sys
import os
from pathlib import Path
import toml
import shutil
import subprocess

def get_enhanced_version():
    """获取增强版本号"""
    try:
        data = toml.load('pyproject.toml')
        base_version = data['project']['version']
        return f"{base_version}_enhanced"
    except Exception:
        return "0.1.0_enhanced"

def prepare_enhanced_data():
    """准备增强特征数据"""
    enhanced_path = "data/enterprise_risk_sample_data_enhanced.csv"
    original_path = "data/enterprise_risk_sample_data.csv"
    temp_path = "data/enterprise_risk_sample_data_temp_backup.csv"
    
    if not os.path.exists(enhanced_path):
        print("❌ 未找到增强特征数据！")
        print("请先运行: python scripts/feature_engineering_enhancement.py")
        return False
    
    # 备份原始数据
    if os.path.exists(original_path):
        shutil.copy2(original_path, temp_path)
        print(f"📄 备份原始数据到: {temp_path}")
    
    # 临时替换数据文件
    shutil.copy2(enhanced_path, original_path)
    print(f"🔄 临时使用增强数据: {enhanced_path}")
    
    return True

def restore_original_data():
    """恢复原始数据"""
    original_path = "data/enterprise_risk_sample_data.csv"
    temp_path = "data/enterprise_risk_sample_data_temp_backup.csv"
    
    if os.path.exists(temp_path):
        shutil.copy2(temp_path, original_path)
        os.remove(temp_path)
        print(f"🔄 恢复原始数据: {original_path}")

def update_output_version():
    """更新输出版本号"""
    enhanced_version = get_enhanced_version()
    
    # 临时修改 pyproject.toml
    try:
        with open('pyproject.toml', 'r') as f:
            content = f.read()
        
        # 备份原始内容
        with open('pyproject_temp_backup.toml', 'w') as f:
            f.write(content)
        
        # 修改版本号
        data = toml.load('pyproject.toml')
        original_version = data['project']['version']
        data['project']['version'] = enhanced_version
        
        with open('pyproject.toml', 'w') as f:
            toml.dump(data, f)
        
        print(f"📦 临时版本号: {original_version} → {enhanced_version}")
        return original_version
        
    except Exception as e:
        print(f"⚠️ 版本号更新失败: {e}")
        return None

def restore_original_version(original_version):
    """恢复原始版本号"""
    backup_path = 'pyproject_temp_backup.toml'
    
    if os.path.exists(backup_path):
        shutil.copy2(backup_path, 'pyproject.toml')
        os.remove(backup_path)
        print(f"🔄 恢复原始版本号: {original_version}")

def main():
    """主函数"""
    print("🚀 增强特征模型训练流水线")
    print("=" * 50)
    
    # 1. 准备增强数据
    if not prepare_enhanced_data():
        return
    
    # 2. 更新版本号
    original_version = update_output_version()
    
    try:
        # 3. 运行原始pipeline
        print("\n🔄 运行增强特征训练流水线...")

        # 直接调用 run_pipeline.py
        import subprocess
        result = subprocess.run([
            sys.executable, "scripts/run_pipeline.py"
        ], capture_output=True, text=True, cwd=".")

        if result.returncode == 0:
            print("✅ 增强特征训练完成")
        else:
            print(f"❌ 训练失败: {result.stderr}")
            return
        
        enhanced_version = get_enhanced_version()
        print(f"\n🎯 增强特征训练完成！")
        print(f"📂 输出目录: outputs/{enhanced_version}/")
        print(f"📊 与原版本对比: outputs/0.1.0/ vs outputs/{enhanced_version}/")
        
        # 4. 生成对比报告
        generate_enhancement_report(enhanced_version)
        
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        
    finally:
        # 5. 恢复原始状态
        restore_original_data()
        if original_version:
            restore_original_version(original_version)

def generate_enhancement_report(enhanced_version):
    """生成增强特征效果报告"""
    report_path = f"outputs/{enhanced_version}/enhancement_report.md"
    
    try:
        # 检查是否存在基线版本
        baseline_path = "outputs/0.1.0/evaluation/evaluation_results.json"
        enhanced_path = f"outputs/{enhanced_version}/evaluation/evaluation_results.json"
        
        if os.path.exists(baseline_path) and os.path.exists(enhanced_path):
            import json
            
            with open(baseline_path, 'r') as f:
                baseline_results = json.load(f)
            
            with open(enhanced_path, 'r') as f:
                enhanced_results = json.load(f)
            
            # 生成对比报告
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(f"# 增强特征效果报告\n\n")
                f.write(f"**基线版本**: 0.1.0\n")
                f.write(f"**增强版本**: {enhanced_version}\n")
                f.write(f"**生成时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                f.write("## 📊 性能对比\n\n")
                f.write("| 指标 | 基线版本 | 增强版本 | 提升 |\n")
                f.write("|------|----------|----------|------|\n")
                
                # 对比关键指标
                for metric in ['test_auc', 'test_ks', 'test_accuracy']:
                    if metric in baseline_results and metric in enhanced_results:
                        baseline_val = baseline_results[metric]
                        enhanced_val = enhanced_results[metric]
                        improvement = enhanced_val - baseline_val
                        f.write(f"| {metric.upper()} | {baseline_val:.4f} | {enhanced_val:.4f} | {improvement:+.4f} |\n")
                
                f.write("\n## 💡 结论\n\n")
                if enhanced_results.get('test_auc', 0) > baseline_results.get('test_auc', 0):
                    f.write("✅ 增强特征显著提升了模型性能\n")
                else:
                    f.write("⚠️ 增强特征未显著提升模型性能，建议进一步优化\n")
            
            print(f"📄 增强效果报告: {report_path}")
            
    except Exception as e:
        print(f"⚠️ 生成增强报告失败: {e}")

if __name__ == "__main__":
    # 确保pandas可用（用于时间戳）
    try:
        import pandas as pd
    except ImportError:
        import datetime
        pd = type('pd', (), {'Timestamp': type('Timestamp', (), {
            'now': lambda: type('now', (), {
                'strftime': lambda fmt: datetime.datetime.now().strftime(fmt)
            })()
        })()})()
    
    main()
