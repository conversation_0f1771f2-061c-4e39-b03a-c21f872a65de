#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
黄金样本分析脚本
基于 stage1.md 对话结果生成的分析脚本
分析模型比传统分数判断更准确的"黄金样本"
"""

import sys
import os
import glob
import pandas as pd
import numpy as np
from sklearn.metrics import roc_auc_score, roc_curve
import argparse
import joblib
import shap
import matplotlib.pyplot as plt


def find_latest_compare_csv():
    """查找最新版本的对比数据文件"""
    outputs_dir = "outputs"
    if not os.path.exists(outputs_dir):
        print("❌ outputs目录不存在！")
        return None, None

    # 获取所有版本号文件夹
    version_dirs = [d for d in os.listdir(outputs_dir)
                   if os.path.isdir(os.path.join(outputs_dir, d)) and not d.startswith('.')]
    # 过滤掉非版本号文件夹
    version_dirs = [d for d in version_dirs if any(c.isdigit() for c in d)]

    if not version_dirs:
        print("❌ 未找到任何版本输出目录！")
        return None, None

    # 按版本号排序（假设语义化版本）
    version_dirs.sort(key=lambda s: [int(x) for x in s.split('.') if x.isdigit()], reverse=True)

    for v in version_dirs:
        compare_path = os.path.join(outputs_dir, v, "evaluation", "model_scorecard_compare.csv")
        if os.path.exists(compare_path):
            return compare_path, v

    print("❌ 未找到任何 compare.csv 文件！")
    return None, None


def find_optimal_threshold(y_true, y_score):
    """使用约登指数找到最优阈值"""
    fpr, tpr, thresholds = roc_curve(y_true, y_score)
    youden = tpr - fpr
    best_idx = np.argmax(youden)
    return thresholds[best_idx]


def analyze_golden_samples(df, subset_name="测试集"):
    """分析黄金样本 - 模型正确但传统分数错误的样本"""
    print(f"\n=== {subset_name} 黄金样本分析 ===")

    # 检查必要字段
    required_cols = ['label', 'traditional_total_score', 'model_score']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        print(f"❌ 缺少必要字段: {missing_cols}")
        return None

    y_true = df['label']
    traditional_score = -df['traditional_total_score']  # 取负值，分数越高风险越高
    model_score = df['model_score']

    # 找到最优阈值
    trad_threshold = find_optimal_threshold(y_true, traditional_score)
    model_threshold = find_optimal_threshold(y_true, model_score)

    print(f"传统分数最优阈值: {trad_threshold:.4f}")
    print(f"模型分数最优阈值: {model_threshold:.4f}")

    # 生成预测结果
    trad_pred = (traditional_score >= trad_threshold).astype(int)
    model_pred = (model_score >= model_threshold).astype(int)

    # 分析四种情况
    both_correct = (trad_pred == y_true) & (model_pred == y_true)
    both_wrong = (trad_pred != y_true) & (model_pred != y_true)
    model_correct_trad_wrong = (model_pred == y_true) & (trad_pred != y_true)
    trad_correct_model_wrong = (trad_pred == y_true) & (model_pred != y_true)

    print(f"\n📊 判断结果统计:")
    print(f"  两者都正确: {both_correct.sum()} 个样本")
    print(f"  两者都错误: {both_wrong.sum()} 个样本")
    print(f"  模型正确，传统错误: {model_correct_trad_wrong.sum()} 个样本 ⭐")
    print(f"  传统正确，模型错误: {trad_correct_model_wrong.sum()} 个样本")

    # 提取黄金样本
    golden_samples = df[model_correct_trad_wrong].copy()
    if len(golden_samples) > 0:
        golden_samples['trad_pred'] = trad_pred[model_correct_trad_wrong]
        golden_samples['model_pred'] = model_pred[model_correct_trad_wrong]

        print(f"\n🏆 黄金样本详情 (前10个):")
        print("=" * 100)
        display_cols = ['enterprise_id', 'traditional_total_score', 'model_score', 'label', 'trad_pred', 'model_pred']
        available_cols = [col for col in display_cols if col in golden_samples.columns]

        if 'enterprise_id' not in golden_samples.columns:
            golden_samples['enterprise_id'] = [f'ENT_{i:06d}' for i in range(len(golden_samples))]
            available_cols = ['enterprise_id'] + [col for col in available_cols if col != 'enterprise_id']

        print(golden_samples[available_cols].head(10).to_string(index=False))

        # 分析黄金样本特征
        print(f"\n📈 黄金样本特征分析:")
        print(f"  传统分数范围: {golden_samples['traditional_total_score'].min():.2f} - {golden_samples['traditional_total_score'].max():.2f}")
        print(f"  传统分数均值: {golden_samples['traditional_total_score'].mean():.2f}")
        print(f"  模型分数范围: {golden_samples['model_score'].min():.4f} - {golden_samples['model_score'].max():.4f}")
        print(f"  模型分数均值: {golden_samples['model_score'].mean():.4f}")

        return {
            'golden_samples': golden_samples,
            'stats': {
                'both_correct': both_correct.sum(),
                'both_wrong': both_wrong.sum(),
                'model_correct_trad_wrong': model_correct_trad_wrong.sum(),
                'trad_correct_model_wrong': trad_correct_model_wrong.sum(),
                'total_samples': len(df)
            },
            'thresholds': {
                'traditional': trad_threshold,
                'model': model_threshold
            }
        }
    else:
        print("❌ 未找到黄金样本！")
        return None


def save_analysis_results(results, output_dir, subset_name):
    """保存分析结果到文件"""
    if not results:
        return
    
    # 保存黄金样本详情
    golden_samples_path = os.path.join(output_dir, f"golden_samples_{subset_name.lower()}.csv")
    results['golden_samples'].to_csv(golden_samples_path, index=False, encoding='utf-8')
    print(f"💾 黄金样本详情已保存到: {golden_samples_path}")

    # 保存统计报告
    report_path = os.path.join(output_dir, f"golden_samples_analysis_{subset_name.lower()}.md")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(f"# 黄金样本分析报告 - {subset_name}\n\n")
        f.write(f"**生成时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        f.write("## 📊 判断结果统计\n\n")
        stats = results['stats']
        f.write("| 判断结果 | 样本数量 | 占比 | 说明 |\n")
        f.write("|----------|----------|------|------|\n")
        f.write(f"| 两者都正确 | {stats['both_correct']} | {stats['both_correct']/stats['total_samples']*100:.1f}% | 模型和传统方法都判断正确 |\n")
        f.write(f"| 两者都错误 | {stats['both_wrong']} | {stats['both_wrong']/stats['total_samples']*100:.1f}% | 模型和传统方法都判断错误 |\n")
        f.write(f"| **模型正确，传统错误** | **{stats['model_correct_trad_wrong']}** | **{stats['model_correct_trad_wrong']/stats['total_samples']*100:.1f}%** | **黄金样本 ⭐** |\n")
        f.write(f"| 传统正确，模型错误 | {stats['trad_correct_model_wrong']} | {stats['trad_correct_model_wrong']/stats['total_samples']*100:.1f}% | 传统方法更优的样本 |\n")
        f.write(f"| **总计** | **{stats['total_samples']}** | **100.0%** | 全部样本 |\n\n")

        f.write("## 🎯 最优阈值\n\n")
        f.write("| 方法 | 最优阈值 | 说明 |\n")
        f.write("|------|----------|------|\n")
        f.write(f"| 传统分数 | {results['thresholds']['traditional']:.4f} | 基于约登指数计算 |\n")
        f.write(f"| 模型分数 | {results['thresholds']['model']:.4f} | 基于约登指数计算 |\n\n")

        if len(results['golden_samples']) > 0:
            f.write("## 🏆 黄金样本特征分析\n\n")
            gs = results['golden_samples']

            f.write("| 指标 | 传统分数 | 模型分数 |\n")
            f.write("|------|----------|----------|\n")
            f.write(f"| 最小值 | {gs['traditional_total_score'].min():.2f} | {gs['model_score'].min():.4f} |\n")
            f.write(f"| 最大值 | {gs['model_score'].max():.4f} | {gs['model_score'].max():.4f} |\n")
            f.write(f"| 平均值 | {gs['traditional_total_score'].mean():.2f} | {gs['model_score'].mean():.4f} |\n")
            f.write(f"| 标准差 | {gs['traditional_total_score'].std():.2f} | {gs['model_score'].std():.4f} |\n\n")

            f.write("## 💡 核心结论\n\n")
            model_advantage = results['stats']['model_correct_trad_wrong']/(results['stats']['model_correct_trad_wrong']+results['stats']['trad_correct_model_wrong'])*100
            f.write(f"- 🎯 **模型优势明显**: 在争议样本中，模型表现优于传统方法\n")
            f.write(f"- 📊 **优势样本**: {results['stats']['model_correct_trad_wrong']} 个 vs {results['stats']['trad_correct_model_wrong']} 个\n")
            f.write(f"- 🏆 **胜率**: {model_advantage:.1f}%\n")
            f.write(f"- 💎 **价值**: 这些黄金样本展示了数据驱动方法的优势\n\n")

            f.write("## 🔍 业务洞察\n\n")
            f.write("黄金样本的特点：\n")
            f.write("- 传统评分方法容易误判的'灰色地带'企业\n")
            f.write("- 需要综合多维度信息才能准确评估的复杂案例\n")
            f.write("- 体现了机器学习模型在处理非线性关系方面的优势\n\n")

    print(f"📄 分析报告已保存到: {report_path}")


def run_shap_analysis(model, X, output_dir, subset_name):
    """
    使用SHAP进行模型可解释性分析
    """
    print("\n=== SHAP 模型可解释性分析 ===")

    try:
        # 对于线性模型，使用LinearExplainer
        explainer = shap.LinearExplainer(model, X)
        shap_values = explainer.shap_values(X)

        print("📊 生成SHAP摘要图...")
        # 创建SHAP摘要图
        plt.figure()
        shap.summary_plot(shap_values, X, show=False, plot_size=(10, 8))

        # 美化图形
        plt.title(f'SHAP Feature Importance - {subset_name}')
        plt.tight_layout()

        # 保存图形
        shap_plot_path = os.path.join(output_dir, f"shap_summary_plot_{subset_name.lower()}.png")
        plt.savefig(shap_plot_path)
        plt.close()

        print(f"💾 SHAP摘要图已保存到: {shap_plot_path}")

    except Exception as e:
        print(f"❌ SHAP分析失败: {e}")


def main():
    parser = argparse.ArgumentParser(description='黄金样本分析 - 找出模型优于传统方法的样本')
    parser.add_argument('--subset', choices=['all', 'train', 'test'], default='test',
                       help='分析的数据子集 (默认: test)')
    args = parser.parse_args()

    print("\n=== 黄金样本分析工具 ====")

    # 查找最新的对比数据
    compare_path, version = find_latest_compare_csv()
    if not compare_path:
        print("❌ 未找到对比数据文件！")
        return

    print(f"📂 读取数据: {compare_path} (版本: {version})")
    df = pd.read_csv(compare_path)

    # 根据参数选择数据子集
    subset_mapping = {'all': '全量样本', 'train': '训练集', 'test': '测试集'}
    subset_name = subset_mapping[args.subset]

    if args.subset != 'all' and 'is_test' in df.columns:
        if args.subset == 'test':
            df_subset = df[df['is_test'] == 1].copy()
        else:  # train
            df_subset = df[df['is_test'] == 0].copy()
    else:
        df_subset = df.copy()

    print(f"📊 分析数据集: {subset_name} ({len(df_subset)} 个样本)")

    output_dir = os.path.dirname(compare_path)
    model_path = os.path.join(os.path.dirname(output_dir), "models", "scorecard_model.pkl")
    feature_weights_path = os.path.join(os.path.dirname(output_dir), "feature", "feature_weights.csv")

    analyzed_data_dir = os.path.join(output_dir, "../analyzed_data/evaluation")
    os.makedirs(analyzed_data_dir, exist_ok=True)

    # --- SHAP Analysis Section ---
    if os.path.exists(model_path) and os.path.exists(feature_weights_path):
        print(f"🔍 加载模型: {model_path}")
        try:
            loaded_data = joblib.load(model_path)
            if isinstance(loaded_data, dict) and 'model' in loaded_data:
                model = loaded_data['model']
            else:
                model = loaded_data

            print(f"📖 读取特征列表: {feature_weights_path}")
            feature_weights_df = pd.read_csv(feature_weights_path)
            feature_cols = feature_weights_df['feature_name'].tolist()

            if all(col in df_subset.columns for col in feature_cols):
                X_subset = df_subset[feature_cols]
                run_shap_analysis(model, X_subset, analyzed_data_dir, args.subset)
            else:
                missing_features = [col for col in feature_cols if col not in df_subset.columns]
                print(f"❌ 数据文件中缺少SHAP分析所需的特征: {missing_features}，跳过SHAP分析。")
        except Exception as e:
            print(f"❌ 加载模型或准备SHAP数据时出错: {e}")

    else:
        if not os.path.exists(model_path):
            print(f"❌ 未找到模型文件: {model_path}，跳过SHAP分析。")
        if not os.path.exists(feature_weights_path):
            print(f"❌ 未找到特征权重文件: {feature_weights_path}，跳过SHAP分析。")

    # --- Golden Samples Analysis Section ---
    results = analyze_golden_samples(df_subset, subset_name)

    if results:
        save_analysis_results(results, analyzed_data_dir, args.subset)
        win_denominator = results['stats']['model_correct_trad_wrong'] + results['stats']['trad_correct_model_wrong']
        if win_denominator > 0:
            win_rate = results['stats']['model_correct_trad_wrong'] / win_denominator * 100
            print(f"\n✅ 分析完成！发现 {results['stats']['model_correct_trad_wrong']} 个黄金样本")
            print(f"   模型优势明显，在争议样本中胜率: {win_rate:.1f}%")
        else:
            print(f"\n✅ 分析完成！发现 {results['stats']['model_correct_trad_wrong']} 个黄金样本，无争议样本。")
    else:
        print("❌ 黄金样本分析失败！")


if __name__ == "__main__":
    main()
