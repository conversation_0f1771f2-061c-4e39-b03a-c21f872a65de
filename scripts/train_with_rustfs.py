#!/usr/bin/env python3
"""
训练脚本，确保保存到RustFS
基于现有pipeline流程，增加RustFS存储
"""
import sys
import asyncio
import json
from pathlib import Path
from datetime import datetime

from innovation_model_investigation.pipeline import get_project_version

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from loguru import logger
from src.innovation_model_investigation.api.services.core_training import (
    CoreTrainingEngine,
)
from src.innovation_model_investigation.api.utils.unified_storage import (
    UnifiedModelStorage,
    make_serializable,
)


async def main():
    """主函数"""
    logger.info("🚀 开始训练并保存到RustFS")

    try:
        # 初始化核心训练引擎
        engine = CoreTrainingEngine()

        # 初始化统一存储（强制启用RustFS，并指定到 innovation_health_model 目录）
        storage = UnifiedModelStorage(base_dir="outputs/innovation_health_model", use_rustfs=True)

        # 执行训练（直接输出到 innovation_health_model 目录）
        version, result = await engine.execute_training_with_pipeline(version=get_project_version(), sample_data_path=Path('data/enterprise_risk_sample_data.csv')
        )

        # 确保评估结果存在
        evaluation_results = result.get("evaluation_results")
        if not evaluation_results:
            logger.warning("⚠️ 训练结果中没有评估结果，将创建默认评估结果")
            # 创建默认评估结果
            evaluation_results = {
                "classification_metrics": {
                    "accuracy": 0.0,
                    "precision": 0.0,
                    "recall": 0.0,
                    "f1": 0.0,
                    "auc": 0.0,
                    "ks": 0.0,
                },
                "timestamp": datetime.now().isoformat(),
                "version": version,
                "note": "默认生成的评估结果",
            }
            result["evaluation_results"] = evaluation_results

            # 创建评估结果文件
            output_dir = Path(result["output_dir"])
            evaluation_results_path = output_dir / "evaluation_results.json"
            # 使用make_serializable处理评估结果
            serializable_eval_results = make_serializable(
                evaluation_results, handle_special_floats=True
            )
            with open(evaluation_results_path, "w", encoding="utf-8") as f:
                json.dump(serializable_eval_results, f, ensure_ascii=False, indent=2)

            # 将评估结果文件添加到files字典中
            result["files"]["evaluation_results"] = evaluation_results_path
            logger.info(f"✅ 已创建默认评估结果文件: {evaluation_results_path}")

        # 保存到RustFS
        logger.info("💾 保存到统一存储（包括RustFS）...")
        storage.save_model_version(
            version=version,
            files=result["files"],
            metadata=result["metadata"],
            evaluation_results=evaluation_results,
        )

        # 验证保存结果
        logger.info("🔍 验证保存结果...")
        models = storage.list_models()
        logger.info(f"📋 找到 {len(models)} 个模型:")
        for model in models:
            logger.info(
                f"  - 版本: {model.get('version')}, 来源: {model.get('source')}, 创建时间: {model.get('created_at')}"
            )

        rustfs_models = [m for m in models if m.get("source") == "rustfs"]
        local_models = [m for m in models if m.get("source") == "local"]

        logger.info(
            f"📊 模型统计: 本地={len(local_models)}, RustFS={len(rustfs_models)}"
        )

        if rustfs_models:
            logger.info(f"✅ RustFS保存成功！找到 {len(rustfs_models)} 个RustFS模型")
            for model in rustfs_models:
                logger.info(
                    f"  - 版本: {model.get('version')}, 创建时间: {model.get('created_at')}"
                )
        else:
            logger.warning("⚠️ 未找到RustFS模型，但本地保存成功")

        logger.info(f"🎉 训练完成: 版本={version}")

    except Exception as e:
        logger.exception(f"❌ 训练失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
