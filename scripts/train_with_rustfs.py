#!/usr/bin/env python3
"""
训练脚本，确保保存到RustFS
基于现有pipeline流程，增加RustFS存储
"""
import sys
from pathlib import Path

from innovation_model_investigation.pipeline import get_project_version, main as run_pipeline

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from loguru import logger
from src.innovation_model_investigation.api.utils.rustfs_storage import (
    RustFSModelStorage,
)


def main():
    """主函数"""
    logger.info("🚀 开始训练并保存到RustFS")

    try:
        # 获取版本号
        version = get_project_version()

        # 执行pipeline训练（保存到outputs/<version>）
        logger.info(f"📊 执行pipeline训练，版本: {version}")
        _ = run_pipeline(
            version=version,
            sample_data_path='data/enterprise_risk_sample_data.csv'
        )

        # 初始化RustFS存储并上传
        logger.info("💾 保存到RustFS...")
        storage = RustFSModelStorage(base_dir="outputs")
        storage.save_model_from_outputs(version=version)

        # 验证保存结果
        logger.info("🔍 验证保存结果...")
        models = storage.list_models()
        logger.info(f"📋 找到 {len(models)} 个模型:")
        for model in models:
            logger.info(
                f"  - 版本: {model.get('version')}, 来源: {model.get('source')}, 创建时间: {model.get('created_at')}"
            )

        rustfs_models = [m for m in models if m.get("source") == "rustfs"]
        local_models = [m for m in models if m.get("source") == "local"]

        logger.info(
            f"📊 模型统计: 本地={len(local_models)}, RustFS={len(rustfs_models)}"
        )

        if rustfs_models:
            logger.info(f"✅ RustFS保存成功！找到 {len(rustfs_models)} 个RustFS模型")
            for model in rustfs_models:
                logger.info(
                    f"  - 版本: {model.get('version')}, 创建时间: {model.get('created_at')}"
                )
        else:
            logger.warning("⚠️ 未找到RustFS模型，但本地保存成功")

        logger.info(f"🎉 训练完成: 版本={version}")

    except Exception as e:
        logger.exception(f"❌ 训练失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
