# 输出目录结构说明

## 📁 重新设计的清晰结构

```
outputs/
├── <version>/                    # 版本目录（如 0.1.0, 0.1.0_enhanced）
│   ├── 🆕 baseline/              # 统一对比基准数据目录
│   │   ├── baseline_performance.json    # 完整性能基准（整合evaluation_results.json）
│   │   ├── comparison_results.json      # 详细对比分析结果
│   │   └── model_scorecard_compare.csv  # 样本级对比数据（从evaluation/复制）
│   ├── 📊 正常输出目录（生产环境使用）
│   │   ├── binning/              # 分箱结果
│   │   │   ├── binning_results.pkl      # 分箱模型
│   │   │   └── iv_ranking.csv           # IV值排序
│   │   ├── feature/              # 特征结果
│   │   │   └── feature_weights.csv      # 特征权重
│   │   ├── models/               # 模型文件
│   │   │   ├── scorecard_model.pkl      # 评分卡模型
│   │   │   └── woe_encoder.pkl          # WOE编码器
│   │   └── visualization/        # 可视化结果
│   │       └── *.png             # 各种图表
│   └── 📈 evaluation/            # 评估结果（正常输出，但也用于对比）
│       ├── evaluation_results.json      # 模型评估结果（正常输出）
│       └── model_scorecard_compare.csv  # 全量样本对比数据（对比用）
└── baseline_comparison_report_*.md      # 基于baseline的统一对比报告
```

## 🎯 目录功能重新定义

### 1. 📊 正常输出目录（生产环境使用）

这些目录存放模型训练的正常输出，用于生产环境部署：

- **`models/`**: 训练好的模型文件
  - `scorecard_model.pkl`: 评分卡模型
  - `woe_encoder.pkl`: WOE编码器

- **`feature/`**: 特征工程结果
  - `feature_weights.csv`: 学习到的特征权重

- **`binning/`**: 分箱结果
  - `binning_results.pkl`: 分箱模型
  - `iv_ranking.csv`: 特征IV值排序

- **`visualization/`**: 可视化图表
  - 各种分析图表和可视化结果

### 2. 📈 evaluation/ 目录（双重用途）

- **正常输出**: `evaluation_results.json` - 模型性能评估结果
- **对比数据**: `model_scorecard_compare.csv` - 全量样本对比数据

### 3. 🆕 baseline/ 目录（统一对比基准）

**这是解决对比逻辑分散问题的核心目录**

- **`baseline_performance.json`**:
  - 整合了 `evaluation_results.json` 的关键信息
  - 包含模型性能、传统方法性能、数据摘要
  - 包含详细评估信息（混淆矩阵、分类报告等）

- **`comparison_results.json`**:
  - 详细的对比分析结果
  - 分层对比（全量/训练/测试）
  - 数据来源说明

- **`model_scorecard_compare.csv`**:
  - 从 `evaluation/` 复制的样本级对比数据
  - 便于对比脚本直接使用

## 🔄 对比逻辑统一规划

### 原有问题
- 多个脚本重复实现对比逻辑
- 对比数据分散在不同位置
- 功能重合，架构不清晰

### 新的统一架构

#### 1. 数据生成层（run_pipeline.py）
```python
# 每次训练后自动生成 baseline 数据
outputs/<version>/baseline/
├── baseline_performance.json    # 性能指标
└── baseline_summary.json        # 对比摘要
```

#### 2. 对比分析层（统一对比管理系统）
```python
scripts/unified_comparison_manager.py
├── 模型 vs 传统方法对比（单版本内部）
├── 版本间对比（时间维度）
└── 黄金样本分析
```

#### 3. 专用对比脚本（保留但功能明确）
```python
scripts/compare_scorecard_performance.py  # 专注于详细的方法对比
scripts/model_analyze_golden_samples.py   # 专注于黄金样本深度分析
```

## 📊 对比数据流向

### 1. 训练阶段（run_pipeline.py）
```
原始数据 → 模型训练 → 生成以下文件：
├── evaluation/evaluation_results.json      # 模型评估结果
├── evaluation/model_scorecard_compare.csv  # 全量对比数据
├── baseline/baseline_performance.json      # 性能基准
└── baseline/baseline_summary.json          # 对比摘要
```

### 2. 对比分析阶段
```
baseline/ 数据 → 统一对比管理器 → 生成对比报告
```

## 🎯 脚本功能重新定位

### 核心对比脚本
- **`unified_comparison_manager.py`**: 
  - 主要对比工具
  - 自动从 baseline/ 读取数据
  - 支持多种对比类型
  - 生成统一报告

### 专用分析脚本
- **`compare_scorecard_performance.py`**: 
  - 专注于详细的ROC曲线、混淆矩阵等深度分析
  - 生成详细的性能对比报告
  
- **`model_analyze_golden_samples.py`**: 
  - 专注于黄金样本的深度挖掘
  - 支持多版本黄金样本演进分析

### 监控脚本
- **`model_monitor.py`**: 
  - 专注于生产环境的实时监控
  - 数据漂移检测
  - 性能衰减告警

## 💡 使用建议

### 日常开发流程
```bash
# 1. 训练模型（自动生成baseline数据）
python run_pipeline.py

# 2. 快速对比分析
python scripts/unified_comparison_manager.py

# 3. 详细分析（可选）
python scripts/compare_scorecard_performance.py
python scripts/model_analyze_golden_samples.py --multi-version
```

### 版本对比
```bash
# 对比最近两个版本
python scripts/unified_comparison_manager.py --type version_comparison

# 分析黄金样本演进
python scripts/unified_comparison_manager.py --type golden_samples
```

## 🔧 迁移指南

### 现有脚本适配
1. **model_compare_traditional.py** → 重命名为 `compare_scorecard_performance.py`
2. 所有对比脚本改为从 `baseline/` 目录读取数据
3. 使用统一的对比管理器作为主要工具

### 数据迁移
1. 重新运行 `run_pipeline.py` 生成完整的 baseline 数据
2. 旧版本可以通过统一对比管理器自动生成 baseline 数据

这个新架构彻底解决了对比逻辑分散、功能重合的问题，提供了清晰、统一、易维护的对比分析体系。
