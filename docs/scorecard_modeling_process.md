# 科创企业健康性评估建模流程指南

## 目录
- [科创企业健康性评估概述](#科创企业健康性评估概述)
- [建模流程](#建模流程)
- [数据准备阶段](#数据准备阶段)
- [特征工程阶段](#特征工程阶段)
- [模型训练阶段](#模型训练阶段)
- [模型验证阶段](#模型验证阶段)
- [健康性评分转换](#健康性评分转换)
- [模型部署与监控](#模型部署与监控)
- [科创企业评估最佳实践](#科创企业评估最佳实践)

---

## 科创企业健康性评估概述

### 什么是科创企业健康性评估
科创企业健康性评估是一种基于逻辑回归的企业综合健康状况评估工具，通过将科创企业的多维度特征转换为标准化分数，来预测其发展健康程度和潜在风险。

### 科创企业健康性评估的特点
- **多维度评估**: 技术创新、市场能力、财务健康、治理结构四大维度
- **动态性强**: 适应科创企业快速发展变化的特点
- **前瞻性**: 不仅评估当前状态，更关注发展潜力
- **可解释性**: 每个指标的贡献度清晰可见，便于投资决策

### 科创企业评估的优势
- **科学性**: 基于数据驱动，减少主观判断偏差
- **全面性**: 综合考虑技术、市场、财务、治理等多个维度
- **适应性**: 能够适应不同发展阶段的科创企业
- **监管友好**: 符合科创企业监管和投资评估要求

### 科创企业健康性评估类型
- **初创期评估**: 重点关注技术创新和团队能力
- **成长期评估**: 平衡技术优势与市场表现
- **成熟期评估**: 综合评估可持续发展能力

---

## 建模流程

```mermaid
graph TD
    A[科创企业数据收集] --> B[数据质量检查]
    B --> C[科创指标体系构建]
    C --> D[特征工程]
    D --> E[科创特色分箱]
    E --> F[WOE编码]
    F --> G[模型训练]
    G --> H[模型验证]
    H --> I[健康性评分转换]
    I --> J[模型部署]
    J --> K[持续监控]
```

---

## 数据准备阶段

### 1. 科创企业数据收集
```python
# 示例：加载科创企业健康性数据
import pandas as pd
from src.innovation_model_investigation.data.loader import DataLoader

loader = DataLoader()
data = loader.load_data('data/enterprise_risk_sample_data.csv')

# 科创企业数据结构
print(f"数据形状: {data.shape}")
print(f"健康性标签分布:\n{data['risk_label'].value_counts()}")

# 科创企业特有的数据字段
innovation_features = [
    'rd_investment_ratio',      # 研发投入比例
    'patent_count',             # 专利数量
    'tech_team_ratio',          # 技术团队占比
    'market_share',             # 市场份额
    'customer_concentration',   # 客户集中度
    'revenue_growth_rate',      # 营收增长率
    'cash_flow_stability',      # 现金流稳定性
    'governance_score'          # 治理评分
]
```

### 2. 科创企业数据质量检查
```python
from src.innovation_model_investigation.data.validator import DataValidator

validator = DataValidator()
validation_report = validator.validate_data(data)

# 科创企业特殊的质量检查
def validate_innovation_data(data):
    """科创企业数据专项检查"""
    issues = []
    
    # 检查研发投入合理性
    if 'rd_investment_ratio' in data.columns:
        high_rd = (data['rd_investment_ratio'] > 0.5).sum()
        if high_rd / len(data) > 0.1:
            issues.append(f"研发投入比例过高的企业占比{high_rd/len(data):.2%}，需要检查")
    
    # 检查专利数据完整性
    if 'patent_count' in data.columns:
        zero_patents = (data['patent_count'] == 0).sum()
        if zero_patents / len(data) > 0.3:
            issues.append(f"无专利企业占比{zero_patents/len(data):.2%}，可能影响模型效果")
    
    # 检查营收增长率的异常值
    if 'revenue_growth_rate' in data.columns:
        extreme_growth = (abs(data['revenue_growth_rate']) > 2).sum()
        if extreme_growth > 0:
            issues.append(f"发现{extreme_growth}个极端增长率样本，需要处理")
    
    return issues

# 执行科创企业专项检查
innovation_issues = validate_innovation_data(data)
for issue in innovation_issues:
    print(f"⚠️ {issue}")
```

### 3. 科创企业样本定义
- **观察期**: 收集科创企业特征数据的时间窗口（通常12-24个月）
- **表现期**: 观察健康性结果的时间窗口（通常6-12个月）
- **样本筛选**: 排除数据不完整、业务异常的企业样本

### 4. 健康性标签定义
```python
def define_innovation_health_target(data):
    """
    定义科创企业健康性目标变量
    
    科创企业健康性综合评判标准：
    - 技术创新能力持续性
    - 市场表现稳定性
    - 财务状况健康性
    - 治理结构完善性
    """
    health_conditions = []
    
    # 技术创新健康性
    if 'rd_investment_ratio' in data.columns and 'patent_count' in data.columns:
        tech_health = (
            (data['rd_investment_ratio'] >= 0.05) &  # 研发投入≥5%
            (data['patent_count'] > 0)               # 有专利产出
        )
        health_conditions.append(tech_health)
    
    # 市场表现健康性
    if 'revenue_growth_rate' in data.columns and 'market_share' in data.columns:
        market_health = (
            (data['revenue_growth_rate'] > 0) &      # 营收正增长
            (data['market_share'] > 0.01)            # 有一定市场份额
        )
        health_conditions.append(market_health)
    
    # 财务健康性
    if 'cash_flow_stability' in data.columns:
        financial_health = data['cash_flow_stability'] > 0.6  # 现金流稳定
        health_conditions.append(financial_health)
    
    # 综合健康性判断（需要满足多个条件）
    if health_conditions:
        # 至少满足70%的健康条件
        health_score = sum(health_conditions) / len(health_conditions)
        data['is_healthy'] = (health_score >= 0.7).astype(int)
    else:
        # 如果没有足够的条件，使用现有的risk_label
        data['is_healthy'] = (1 - data['risk_label']).astype(int)
    
    return data

# 应用健康性标签定义
data = define_innovation_health_target(data)
print(f"健康企业比例: {data['is_healthy'].mean():.2%}")
```

---

## 特征工程阶段

### 1. 科创企业特征分类
```python
def categorize_innovation_features(data):
    """科创企业特征分类"""
    
    # 技术创新类特征
    tech_features = [
        'rd_investment_ratio',     # 研发投入比例
        'patent_count',            # 专利数量
        'tech_team_ratio',         # 技术团队占比
        'innovation_cycle',        # 创新周期
        'tech_competitiveness'     # 技术竞争力
    ]
    
    # 市场能力类特征
    market_features = [
        'market_share',            # 市场份额
        'customer_concentration',  # 客户集中度
        'brand_recognition',       # 品牌认知度
        'sales_network',           # 销售网络
        'market_expansion_speed'   # 市场扩张速度
    ]
    
    # 财务健康类特征
    financial_features = [
        'revenue_growth_rate',     # 营收增长率
        'profit_margin',           # 利润率
        'cash_flow_stability',     # 现金流稳定性
        'debt_ratio',              # 负债率
        'financing_ability'        # 融资能力
    ]
    
    # 治理结构类特征
    governance_features = [
        'management_experience',   # 管理团队经验
        'board_independence',      # 董事会独立性
        'internal_control',        # 内控制度
        'information_disclosure',  # 信息披露
        'stakeholder_relations'    # 利益相关者关系
    ]
    
    # 筛选实际存在的特征
    available_features = data.columns.tolist()
    
    return {
        'tech': [f for f in tech_features if f in available_features],
        'market': [f for f in market_features if f in available_features],
        'financial': [f for f in financial_features if f in available_features],
        'governance': [f for f in governance_features if f in available_features]
    }

# 特征分类
feature_categories = categorize_innovation_features(data)
print("科创企业特征分类:")
for category, features in feature_categories.items():
    print(f"{category}: {len(features)}个特征")
```

### 2. 科创企业缺失值处理策略
```python
from src.innovation_model_investigation.data.preprocessor import DataPreprocessor

preprocessor = DataPreprocessor()

# 科创企业特殊的缺失值处理策略
innovation_missing_strategies = {
    # 技术创新指标：保守填充
    'rd_investment_ratio': 'median',    # 研发投入用中位数
    'patent_count': 'zero',             # 专利数量用0填充
    'tech_team_ratio': 'median',        # 技术团队比例用中位数
    
    # 市场指标：行业基准填充
    'market_share': 'industry_median',   # 用行业中位数
    'customer_concentration': 'median',  # 用中位数
    
    # 财务指标：保守估计
    'revenue_growth_rate': 'zero',       # 增长率用0填充
    'cash_flow_stability': 'low_value',  # 用较低值填充
    
    # 治理指标：平均水平
    'governance_score': 'median'         # 用中位数填充
}

def handle_innovation_missing_values(data, strategies):
    """处理科创企业的缺失值"""
    data_filled = data.copy()
    
    for feature, strategy in strategies.items():
        if feature in data_filled.columns:
            if strategy == 'zero':
                data_filled[feature].fillna(0, inplace=True)
            elif strategy == 'median':
                data_filled[feature].fillna(data_filled[feature].median(), inplace=True)
            elif strategy == 'low_value':
                # 用25%分位数填充，体现保守估计
                data_filled[feature].fillna(data_filled[feature].quantile(0.25), inplace=True)
            elif strategy == 'industry_median':
                # 如果有行业信息，按行业填充；否则用整体中位数
                if 'industry_type' in data_filled.columns:
                    data_filled[feature] = data_filled.groupby('industry_type')[feature].transform(
                        lambda x: x.fillna(x.median())
                    )
                else:
                    data_filled[feature].fillna(data_filled[feature].median(), inplace=True)
    
    return data_filled

# 应用科创企业缺失值处理
clean_data = handle_innovation_missing_values(data, innovation_missing_strategies)
```

### 3. 科创企业异常值处理
```python
def handle_innovation_outliers(data):
    """处理科创企业的异常值"""
    
    # 科创企业的异常值往往有业务含义，需要谨慎处理
    outlier_rules = {
        'rd_investment_ratio': {
            'max_threshold': 0.8,  # 研发投入比例最高80%
            'action': 'cap'        # 封顶处理
        },
        'revenue_growth_rate': {
            'min_threshold': -0.5,  # 营收下降最多50%
            'max_threshold': 5.0,   # 营收增长最多500%
            'action': 'cap'
        },
        'patent_count': {
            'max_threshold': 'p99',  # 用99%分位数封顶
            'action': 'cap'
        }
    }
    
    data_clean = data.copy()
    
    for feature, rule in outlier_rules.items():
        if feature in data_clean.columns:
            if rule['action'] == 'cap':
                if 'min_threshold' in rule:
                    min_val = rule['min_threshold']
                    data_clean[feature] = data_clean[feature].clip(lower=min_val)
                
                if 'max_threshold' in rule:
                    if rule['max_threshold'] == 'p99':
                        max_val = data_clean[feature].quantile(0.99)
                    else:
                        max_val = rule['max_threshold']
                    data_clean[feature] = data_clean[feature].clip(upper=max_val)
    
    return data_clean

# 处理异常值
clean_data = handle_innovation_outliers(clean_data)
```

### 4. 科创企业特征衍生
```python
def create_innovation_derived_features(data):
    """创建科创企业衍生特征"""
    derived_data = data.copy()
    
    # 技术创新综合指标
    if all(col in data.columns for col in ['rd_investment_ratio', 'patent_count', 'tech_team_ratio']):
        # 技术创新强度指数
        derived_data['tech_innovation_index'] = (
            derived_data['rd_investment_ratio'] * 0.4 +
            (derived_data['patent_count'] / (derived_data['patent_count'].max() + 1)) * 0.3 +
            derived_data['tech_team_ratio'] * 0.3
        )
    
    # 市场竞争力指标
    if all(col in data.columns for col in ['market_share', 'revenue_growth_rate']):
        # 市场竞争力指数
        derived_data['market_competitiveness'] = (
            derived_data['market_share'] * 0.6 +
            np.maximum(derived_data['revenue_growth_rate'], 0) * 0.4
        )
    
    # 财务稳健性指标
    if all(col in data.columns for col in ['cash_flow_stability', 'debt_ratio']):
        # 财务稳健性指数
        derived_data['financial_stability'] = (
            derived_data['cash_flow_stability'] * 0.7 +
            (1 - derived_data['debt_ratio']) * 0.3
        )
    
    # 发展阶段识别
    if 'company_age' in data.columns and 'revenue_growth_rate' in data.columns:
        def identify_development_stage(row):
            if row['company_age'] <= 3:
                return 'startup'      # 初创期
            elif row['company_age'] <= 8:
                if row['revenue_growth_rate'] > 0.3:
                    return 'growth'   # 高速成长期
                else:
                    return 'stable'   # 稳定期
            else:
                return 'mature'       # 成熟期
        
        derived_data['development_stage'] = derived_data.apply(identify_development_stage, axis=1)
    
    return derived_data

# 创建衍生特征
clean_data = create_innovation_derived_features(clean_data)
```

---

## 特征工程阶段

### 1. 科创企业最优分箱
```python
from src.innovation_model_investigation.features.binning import OptimalBinning

# 初始化分箱器
binning = OptimalBinning()

# 科创企业特征分箱配置
innovation_binning_config = {
    'tech_features': {
        'max_bins': 5,
        'min_bin_size': 0.1,    # 科创企业样本可能较少，适当放宽
        'monotonic': True       # 技术指标通常与健康性单调相关
    },
    'financial_features': {
        'max_bins': 6,
        'min_bin_size': 0.05,
        'monotonic': False      # 财务指标可能存在非线性关系
    },
    'market_features': {
        'max_bins': 4,
        'min_bin_size': 0.1,
        'monotonic': True       # 市场指标通常与健康性正相关
    }
}

# 对科创企业特征进行分箱
binning_results = {}
all_features = []
for category, features in feature_categories.items():
    all_features.extend(features)

for feature in all_features:
    try:
        # 根据特征类型选择分箱配置
        if feature in feature_categories['tech']:
            config = innovation_binning_config['tech_features']
        elif feature in feature_categories['financial']:
            config = innovation_binning_config['financial_features']
        elif feature in feature_categories['market']:
            config = innovation_binning_config['market_features']
        else:
            config = innovation_binning_config['tech_features']  # 默认配置
        
        result = binning.optimal_binning(
            feature=clean_data[feature],
            target=clean_data['is_healthy'],
            max_bins=config['max_bins'],
            min_bin_size=config['min_bin_size']
        )
        binning_results[feature] = result
        print(f"{feature} 分箱完成，IV值: {result['iv']:.4f}")
        
    except Exception as e:
        print(f"{feature} 分箱失败: {str(e)}")
```

### 2. 科创企业WOE编码
```python
from src.innovation_model_investigation.features.woe_encoder import WOEEncoder

# 初始化WOE编码器
woe_encoder = WOEEncoder()

# 拟合WOE编码器
woe_encoder.fit(clean_data[all_features], clean_data['is_healthy'])

# 转换特征
woe_transformed_data = woe_encoder.transform(clean_data[all_features])

print("科创企业WOE转换完成")
print(f"转换后特征数量: {woe_transformed_data.shape[1]}")

# 获取IV值排序
iv_results = woe_encoder.get_feature_iv(clean_data[all_features], clean_data['is_healthy'])
sorted_iv = sorted(iv_results.items(), key=lambda x: x[1], reverse=True)

print("\n科创企业特征IV值排序:")
for feature, iv in sorted_iv[:10]:  # 显示前10个
    print(f"{feature}: {iv:.4f}")
```

### 3. 科创企业特征选择
```python
def select_innovation_features(binning_results, iv_threshold=0.1, business_rules=None):
    """
    科创企业特征选择
    
    考虑因素：
    1. 统计显著性（IV值）
    2. 业务重要性
    3. 监管要求
    4. 数据稳定性
    """
    
    # 基于IV值的初步筛选
    high_iv_features = []
    for feature, result in binning_results.items():
        if result['iv'] >= iv_threshold:
            high_iv_features.append(feature)
    
    # 业务规则筛选
    if business_rules is None:
        business_rules = {
            'must_include': [
                'rd_investment_ratio',    # 研发投入是科创企业核心指标
                'tech_innovation_index',  # 技术创新综合指标
                'market_competitiveness', # 市场竞争力
                'financial_stability'    # 财务稳健性
            ],
            'prefer_include': [
                'patent_count',           # 专利数量
                'revenue_growth_rate',    # 营收增长率
                'governance_score'        # 治理评分
            ]
        }
    
    # 合并必须包含的特征
    selected_features = list(set(high_iv_features + business_rules['must_include']))
    
    # 添加偏好特征（如果IV值合理）
    for feature in business_rules['prefer_include']:
        if feature in binning_results and binning_results[feature]['iv'] >= 0.05:
            selected_features.append(feature)
    
    # 去重并过滤实际存在的特征
    selected_features = list(set(selected_features))
    selected_features = [f for f in selected_features if f in woe_transformed_data.columns]
    
    print(f"基于IV值 >= {iv_threshold} 和业务规则选择的特征数量: {len(selected_features)}")
    
    return selected_features

# 特征选择
selected_features = select_innovation_features(binning_results, iv_threshold=0.08)  # 科创企业适当放宽阈值

# 相关性检查（科创企业特征间可能存在较强相关性）
correlation_matrix = woe_transformed_data[selected_features].corr()
high_corr_pairs = []

for i in range(len(correlation_matrix.columns)):
    for j in range(i+1, len(correlation_matrix.columns)):
        corr_value = abs(correlation_matrix.iloc[i, j])
        if corr_value > 0.7:
            high_corr_pairs.append((
                correlation_matrix.columns[i],
                correlation_matrix.columns[j],
                corr_value
            ))

print(f"\n高相关性特征对数量: {len(high_corr_pairs)}")
if high_corr_pairs:
    print("需要注意的高相关性特征对:")
    for feat1, feat2, corr in high_corr_pairs:
        print(f"  {feat1} - {feat2}: {corr:.3f}")
```

---

## 模型训练阶段

### 1. 科创企业数据集划分
```python
from sklearn.model_selection import train_test_split

# 科创企业数据集划分策略
X = woe_transformed_data[selected_features]
y = clean_data['is_healthy']

# 考虑科创企业可能的样本不平衡问题
print(f"健康企业比例: {y.mean():.2%}")
print(f"不健康企业比例: {(1-y).mean():.2%}")

# 分层抽样确保训练集和测试集的标签分布一致
X_train, X_test, y_train, y_test = train_test_split(
    X, y, 
    test_size=0.3, 
    random_state=42, 
    stratify=y  # 分层抽样
)

print(f"训练集样本数: {X_train.shape[0]}")
print(f"测试集样本数: {X_test.shape[0]}")
print(f"训练集健康企业比例: {y_train.mean():.2%}")
print(f"测试集健康企业比例: {y_test.mean():.2%}")

# 如果样本不平衡严重，考虑采样策略
if y.mean() < 0.2 or y.mean() > 0.8:
    print("⚠️ 检测到样本不平衡，建议考虑采样策略或调整类别权重")
```

### 2. 科创企业逻辑回归训练
```python
from src.innovation_model_investigation.models.trainer import ModelTrainer

# 初始化训练器
trainer = ModelTrainer()

# 科创企业模型训练配置
innovation_training_config = {
    'model_params': {
        'class_weight': 'balanced',  # 处理样本不平衡
        'random_state': 42,
        'max_iter': 1000
    },
    'grid_search_params': {
        'C': [0.01, 0.1, 1, 10, 100],           # 正则化参数
        'penalty': ['l1', 'l2', 'elasticnet'],   # 正则化类型
        'solver': ['liblinear', 'saga']          # 求解器
    },
    'cv_folds': 5,
    'scoring': 'roc_auc'  # 使用AUC作为评估指标
}

# 训练模型
training_result = trainer.train_model(
    X_train, y_train, 
    config=innovation_training_config
)

print("科创企业健康性评估模型训练完成")
print(f"最佳参数: {training_result['best_params']}")
print(f"交叉验证AUC: {training_result['cv_auc']:.4f}")
print(f"交叉验证AUC标准差: {training_result['cv_auc_std']:.4f}")
```

### 3. 科创企业模型系数解释
```python
def interpret_innovation_model_coefficients(model, feature_names, feature_categories):
    """解释科创企业模型系数"""
    
    coefficients = pd.DataFrame({
        'feature': feature_names,
        'coefficient': model.coef_[0],
        'abs_coefficient': abs(model.coef_[0])
    }).sort_values('abs_coefficient', ascending=False)
    
    # 添加特征类别信息
    def get_feature_category(feature):
        for category, features in feature_categories.items():
            if feature in features:
                return category
        return 'derived'  # 衍生特征
    
    coefficients['category'] = coefficients['feature'].apply(get_feature_category)
    
    print("科创企业健康性评估模型特征重要性排序:")
    print(coefficients[['feature', 'category', 'coefficient', 'abs_coefficient']])
    
    # 按类别分析权重分布
    print("\n各类别特征权重分析:")
    category_weights = coefficients.groupby('category').agg({
        'abs_coefficient': ['sum', 'mean', 'count']
    }).round(4)
    print(category_weights)
    
    return coefficients

# 解释模型
model = training_result['model']
coefficients = interpret_innovation_model_coefficients(
    model, selected_features, feature_categories
)
```

---

## 模型验证阶段

### 1. 科创企业模型性能评估
```python
from src.innovation_model_investigation.models.evaluator import ModelEvaluator

# 初始化评估器
evaluator = ModelEvaluator()

# 评估科创企业模型性能
evaluation_results = evaluator.evaluate_model(
    model=model,
    X_test=X_test,
    y_test=y_test,
    X_train=X_train,
    y_train=y_train
)

print("科创企业健康性评估模型评估结果:")
for metric, value in evaluation_results['test_metrics'].items():
    print(f"{metric}: {value:.4f}")

# 科创企业特殊的评估指标
def evaluate_innovation_specific_metrics(model, X_test, y_test):
    """科创企业特殊评估指标"""
    
    y_pred_proba = model.predict_proba(X_test)[:, 1]
    y_pred = model.predict(X_test)
    
    # 不同健康性阈值下的表现
    thresholds = [0.3, 0.5, 0.7]
    threshold_performance = {}
    
    for threshold in thresholds:
        y_pred_threshold = (y_pred_proba >= threshold).astype(int)
        
        from sklearn.metrics import precision_score, recall_score, f1_score
        precision = precision_score(y_test, y_pred_threshold)
        recall = recall_score(y_test, y_pred_threshold)
        f1 = f1_score(y_test, y_pred_threshold)
        
        threshold_performance[threshold] = {
            'precision': precision,
            'recall': recall,
            'f1': f1
        }
    
    return threshold_performance

# 科创企业特殊指标评估
innovation_metrics = evaluate_innovation_specific_metrics(model, X_test, y_test)
print("\n不同健康性阈值下的模型表现:")
for threshold, metrics in innovation_metrics.items():
    print(f"阈值 {threshold}: 精确率={metrics['precision']:.3f}, 召回率={metrics['recall']:.3f}, F1={metrics['f1']:.3f}")
```

### 2. 科创企业模型稳定性验证
```python
def innovation_stability_validation(model, X_train, X_test, feature_categories):
    """科创企业模型稳定性验证"""
    
    # 预测概率
    train_proba = model.predict_proba(X_train)[:, 1]
    test_proba = model.predict_proba(X_test)[:, 1]
    
    # 计算PSI（对科创企业特别重要，因为变化快）
    from src.innovation_model_investigation.utils.metrics import calculate_psi
    psi_value = calculate_psi(train_proba, test_proba)
    
    print(f"科创企业模型PSI值: {psi_value:.4f}")
    
    # PSI评估标准（科创企业适当放宽）
    if psi_value < 0.15:
        print("模型稳定性: 优秀（适合科创企业动态特征）")
    elif psi_value < 0.25:
        print("模型稳定性: 良好（需要定期监控）")
    else:
        print("模型稳定性: 需要关注（建议重新训练）")
    
    # 按特征类别分析稳定性
    category_stability = {}
    for category, features in feature_categories.items():
        category_features = [f for f in features if f in X_train.columns]
        if category_features:
            # 计算该类别特征的预测稳定性
            train_category_pred = model.predict_proba(X_train[category_features])[:, 1]
            test_category_pred = model.predict_proba(X_test[category_features])[:, 1]
            category_psi = calculate_psi(train_category_pred, test_category_pred)
            category_stability[category] = category_psi
    
    print("\n各类别特征稳定性分析:")
    for category, psi in category_stability.items():
        print(f"{category}: PSI={psi:.4f}")
    
    return psi_value, category_stability

# 稳定性验证
psi_value, category_stability = innovation_stability_validation(
    model, X_train, X_test, feature_categories
)
```

### 3. 科创企业业务逻辑验证
```python
def innovation_business_logic_validation(woe_encoder, selected_features, feature_categories):
    """科创企业业务逻辑验证"""
    
    print("科创企业WOE值业务逻辑检查:")
    
    woe_summary = woe_encoder.get_woe_summary()
    
    # 检查关键科创指标的单调性
    key_monotonic_features = [
        'rd_investment_ratio',    # 研发投入应与健康性正相关
        'tech_innovation_index',  # 技术创新指数应与健康性正相关
        'financial_stability'     # 财务稳健性应与健康性正相关
    ]
    
    for feature in key_monotonic_features:
        if feature in woe_summary:
            woe_values = [bin_info['woe'] for bin_info in woe_summary[feature]]
            
            # 检查单调性
            is_increasing = all(woe_values[i] <= woe_values[i+1] for i in range(len(woe_values)-1))
            is_decreasing = all(woe_values[i] >= woe_values[i+1] for i in range(len(woe_values)-1))
            is_monotonic = is_increasing or is_decreasing
            
            expected_direction = "递增" if feature in ['rd_investment_ratio', 'tech_innovation_index', 'financial_stability'] else "递减"
            actual_direction = "递增" if is_increasing else "递减" if is_decreasing else "非单调"
            
            status = "✓" if (expected_direction == "递增" and is_increasing) or (expected_direction == "递减" and is_decreasing) else "⚠️"
            
            print(f"{status} {feature}: 期望{expected_direction}, 实际{actual_direction}")
    
    # 检查特征间的逻辑关系
    print("\n特征间逻辑关系检查:")
    
    # 技术创新与市场表现的关系
    tech_features = [f for f in feature_categories['tech'] if f in selected_features]
    market_features = [f for f in feature_categories['market'] if f in selected_features]
    
    if tech_features and market_features:
        print(f"技术创新特征({len(tech_features)}个) 与 市场特征({len(market_features)}个) 均被选入模型 ✓")
    else:
        print("⚠️ 技术创新与市场表现特征不均衡，可能影响模型全面性")

# 业务逻辑验证
innovation_business_logic_validation(woe_encoder, selected_features, feature_categories)
```

---

## 健康性评分转换

### 1. 科创企业评分公式
科创企业健康性评分的标准公式：
```
Score = A - B × ln(odds)
```
其中：
- A: 基准分数（科创企业通常设置为700分）
- B: 刻度参数（控制分数变化幅度）
- odds: 不健康概率比 P(unhealthy) / P(healthy)

### 2. 科创企业评分参数设置
```python
def set_innovation_scorecard_parameters(base_score=700, base_odds=20, pdo=30):
    """
    设置科创企业健康性评分参数
    
    Args:
        base_score: 基准分数 (科创企业建议700分)
        base_odds: 基准比率 (1:20，即5%不健康率)
        pdo: Points to Double Odds (比率翻倍时的分数变化，科创企业建议30分)
    
    Returns:
        A, B参数
    """
    import math
    
    B = pdo / math.log(2)
    A = base_score + B * math.log(base_odds)
    
    return A, B

# 设置科创企业评分参数
A, B = set_innovation_scorecard_parameters()
print(f"科创企业评分参数 A: {A:.2f}")
print(f"科创企业评分参数 B: {B:.2f}")
```

### 3. 科创企业健康性评分计算
```python
def calculate_innovation_health_score(model, X, A, B):
    """计算科创企业健康性评分"""
    import numpy as np
    
    # 获取不健康概率
    prob_unhealthy = model.predict_proba(X)[:, 0]  # 注意：这里是不健康概率
    prob_healthy = model.predict_proba(X)[:, 1]    # 健康概率
    
    # 避免除零错误
    prob_unhealthy = np.clip(prob_unhealthy, 1e-6, 1-1e-6)
    prob_healthy = np.clip(prob_healthy, 1e-6, 1-1e-6)
    
    # 计算odds
    odds = prob_unhealthy / prob_healthy
    
    # 计算分数
    scores = A - B * np.log(odds)
    
    return scores

# 计算测试集健康性评分
test_scores = calculate_innovation_health_score(model, X_test, A, B)

print(f"科创企业健康性评分统计:")
print(f"平均分: {test_scores.mean():.2f}")
print(f"标准差: {test_scores.std():.2f}")
print(f"最低分: {test_scores.min():.2f}")
print(f"最高分: {test_scores.max():.2f}")
```

### 4. 科创企业评分分布分析
```python
def innovation_score_distribution_analysis(scores, target, bins=10):
    """科创企业评分分布分析"""
    df = pd.DataFrame({'score': scores, 'is_healthy': target})
    df['score_bin'] = pd.qcut(df['score'], bins, duplicates='drop')
    
    analysis = df.groupby('score_bin').agg({
        'is_healthy': ['count', 'sum', 'mean'],
        'score': ['min', 'max', 'mean']
    }).round(4)
    
    analysis.columns = ['样本数', '健康企业数', '健康率', '最低分', '最高分', '平均分']
    
    # 添加科创企业评级
    def get_innovation_rating(healthy_rate):
        if healthy_rate >= 0.9:
            return 'AAA'
        elif healthy_rate >= 0.8:
            return 'AA'
        elif healthy_rate >= 0.7:
            return 'A'
        elif healthy_rate >= 0.6:
            return 'BBB'
        elif healthy_rate >= 0.5:
            return 'BB'
        else:
            return 'B'
    
    analysis['科创评级'] = analysis['健康率'].apply(get_innovation_rating)
    
    return analysis

# 分数分布分析
score_analysis = innovation_score_distribution_analysis(test_scores, y_test)
print("科创企业健康性评分分布分析:")
print(score_analysis)
```

---

## 模型部署与监控

### 1. 科创企业模型持久化
```python
import joblib
import json

# 保存科创企业健康性评估模型
joblib.dump(model, 'outputs/innovation_health_model.pkl')
joblib.dump(woe_encoder, 'outputs/innovation_woe_encoder.pkl')
joblib.dump(binning_results, 'outputs/innovation_binning_results.pkl')

# 保存科创企业评分卡参数
innovation_scorecard_params = {
    'A': A,
    'B': B,
    'selected_features': selected_features,
    'feature_categories': feature_categories,
    'feature_coefficients': coefficients.to_dict('records'),
    'model_version': '1.0',
    'model_type': 'innovation_health_assessment',
    'training_date': pd.Timestamp.now().isoformat()
}

with open('outputs/innovation_scorecard_params.json', 'w', encoding='utf-8') as f:
    json.dump(innovation_scorecard_params, f, ensure_ascii=False, indent=2)

print("科创企业健康性评估模型保存完成")
```

### 2. 科创企业生产环境部署
```python
class InnovationHealthScorecardModel:
    """科创企业健康性评估模型"""
    
    def __init__(self, model_path, encoder_path, params_path):
        self.model = joblib.load(model_path)
        self.woe_encoder = joblib.load(encoder_path)
        
        with open(params_path, 'r', encoding='utf-8') as f:
            self.params = json.load(f)
        
        self.A = self.params['A']
        self.B = self.params['B']
        self.selected_features = self.params['selected_features']
        self.feature_categories = self.params['feature_categories']
    
    def predict_health_score(self, X):
        """预测科创企业健康性评分"""
        # 数据预处理
        X_processed = self.preprocess_innovation_data(X)
        
        # WOE转换
        X_woe = self.woe_encoder.transform(X_processed)
        X_selected = X_woe[self.selected_features]
        
        # 预测概率
        prob_healthy = self.model.predict_proba(X_selected)[:, 1]
        prob_unhealthy = 1 - prob_healthy
        
        # 计算分数
        odds = prob_unhealthy / prob_healthy
        scores = self.A - self.B * np.log(odds)
        
        # 健康性评级
        ratings = self.get_health_rating(scores)
        
        return {
            'scores': scores,
            'probabilities': prob_healthy,
            'ratings': ratings
        }
    
    def preprocess_innovation_data(self, X):
        """科创企业数据预处理"""
        X_processed = X.copy()
        
        # 应用相同的缺失值处理策略
        X_processed = handle_innovation_missing_values(X_processed, innovation_missing_strategies)
        
        # 应用相同的异常值处理
        X_processed = handle_innovation_outliers(X_processed)
        
        # 创建衍生特征
        X_processed = create_innovation_derived_features(X_processed)
        
        return X_processed
    
    def get_health_rating(self, scores):
        """获取健康性评级"""
        ratings = []
        for score in scores:
            if score >= 750:
                ratings.append('AAA')
            elif score >= 720:
                ratings.append('AA')
            elif score >= 690:
                ratings.append('A')
            elif score >= 660:
                ratings.append('BBB')
            elif score >= 630:
                ratings.append('BB')
            else:
                ratings.append('B')
        return ratings

# 部署示例
innovation_scorecard = InnovationHealthScorecardModel(
    'outputs/innovation_health_model.pkl',
    'outputs/innovation_woe_encoder.pkl',
    'outputs/innovation_scorecard_params.json'
)
```

### 3. 科创企业模型监控
```python
def innovation_model_monitoring(scorecard, new_data, reference_scores, monitoring_config=None):
    """科创企业模型监控"""
    
    if monitoring_config is None:
        monitoring_config = {
            'psi_threshold': 0.15,      # 科创企业PSI阈值适当放宽
            'score_drift_threshold': 15, # 分数漂移阈值
            'min_sample_size': 100      # 最小监控样本量
        }
    
    if len(new_data) < monitoring_config['min_sample_size']:
        return {
            'status': 'insufficient_data',
            'message': f"样本量不足，需要至少{monitoring_config['min_sample_size']}个样本"
        }
    
    # 预测新数据
    results = scorecard.predict_health_score(new_data)
    new_scores = results['scores']
    new_proba = results['probabilities']
    
    # 计算PSI
    psi_value = calculate_psi(reference_scores, new_scores)
    
    # 分数分布检查
    score_drift = {
        'mean_diff': new_scores.mean() - reference_scores.mean(),
        'std_diff': new_scores.std() - reference_scores.std(),
        'psi': psi_value
    }
    
    # 预警逻辑
    alerts = []
    if abs(score_drift['mean_diff']) > monitoring_config['score_drift_threshold']:
        alerts.append(f"分数均值偏移{score_drift['mean_diff']:.1f}分，超过阈值")
    
    if psi_value > monitoring_config['psi_threshold']:
        alerts.append(f"PSI值 {psi_value:.4f} 超过阈值，模型可能需要重新训练")
    
    # 健康率监控
    current_health_rate = new_proba.mean()
    expected_health_rate = 0.8  # 假设期望健康率为80%
    
    if abs(current_health_rate - expected_health_rate) > 0.1:
        alerts.append(f"当前健康率{current_health_rate:.2%}偏离期望值较大")
    
    # 生成监控报告
    monitoring_report = {
        'monitoring_date': pd.Timestamp.now().isoformat(),
        'sample_size': len(new_data),
        'score_drift': score_drift,
        'alerts': alerts,
        'new_scores_stats': {
            'mean': new_scores.mean(),
            'std': new_scores.std(),
            'min': new_scores.min(),
            'max': new_scores.max()
        },
        'health_rate': current_health_rate,
        'recommendation': 'normal' if not alerts else 'attention_required'
    }
    
    return monitoring_report

# 监控示例（模拟新数据）
# monitoring_report = innovation_model_monitoring(innovation_scorecard, new_data, test_scores)
# print("科创企业模型监控报告:")
# print(json.dumps(monitoring_report, indent=2, ensure_ascii=False))
```

---

## 科创企业评估最佳实践

### 1. 数据质量管理
- **多源数据整合**: 结合财务报表、专利数据、市场信息等多源数据
- **实时数据更新**: 建立科创企业关键指标的实时监控机制
- **数据验证规则**: 制定科创企业特有的数据质量验证规则

### 2. 特征工程策略
- **行业细分**: 针对不同科创行业（生物医药、人工智能、新能源等）建立专门模型
- **发展阶段**: 考虑科创企业不同发展阶段的特征权重调整
- **动态特征**: 引入时间序列特征捕捉科创企业的发展趋势

### 3. 模型验证要求
- **时间外验证**: 使用未来时期数据验证模型预测能力
- **行业验证**: 在不同科创行业中验证模型的适用性
- **专家验证**: 结合科创投资专家的业务判断进行模型验证

### 4. 监管合规考虑
- **透明度要求**: 确保模型决策过程的可解释性
- **公平性**: 避免对特定类型科创企业的系统性偏见
- **隐私保护**: 保护科创企业的商业机密和技术秘密

### 5. 持续优化机制
- **定期重训**: 根据科创企业发展变化定期重新训练模型
- **特征更新**: 随着科创生态发展引入新的评估维度
- **反馈机制**: 建立投资结果反馈机制，持续优化模型

---

## 总结

科创企业健康性评估建模是一个复杂的系统工程，需要：

1. **深入理解科创企业特点**: 技术导向、高成长性、高风险性
2. **构建科学的评估体系**: 多维度、动态化、前瞻性的指标体系
3. **采用适当的建模方法**: WOE分箱+逻辑回归，平衡准确性与可解释性
4. **建立完善的验证机制**: 统计验证、业务验证、时间验证相结合
5. **实现持续的监控优化**: 适应科创企业快速变化的特点

通过遵循本指南的流程和方法，可以构建出适用于科创企业的健康性评估模型，为科创投资决策提供科学依据，促进科创企业健康发展。 